<template>
  <view>
    <!-- 顶部 -->
    <view
      class="f fac top-view"
      :style="{
        position: 'absolute',
        left: topStyle.left + 'px',
        top: topStyle.top + 'px',
      }"
    >
      <!-- <u-icon name="arrow-left" color="#FFFFFF" size="22"></u-icon> -->
      <view class="info-view ml_12 f fac pl_6 pr_28">
        <u--image
          width="72rpx"
          :src="infoData.user.avatar"
          height="72rpx"
          shape="circle"
        ></u--image>
        <view class="ml_5">
          <view class="anchor-name">{{ infoData.user.nickname }}</view>
          <view class="viewership">{{ infoData.total_num }}观看</view>
        </view>
      </view>
    </view>
    <div class="video-main-div">
      <div id="myVideo" style="width: 100vw; height: 100vh"></div>
      <view class="bottom-view">
        <scroll-view
          :scroll-top="scrollTop"
          scroll-y="true"
          class="message-list-view"
        >
          <view id="messageBox" class="p-20">
            <!-- v-for="(item, index) in 21" -->
            <view
              v-for="(item, index) in messageList"
              :key="index"
              class="message-row"
            >
              <view class="message-view">
                <text class="user-name-text" v-if="item.type === 1">
                  {{ item.user_name }}:
                </text>
                {{ item.message }}
              </view>
              <!-- <view class="message-view">{{ item }}</view> -->
            </view>
          </view>
        </scroll-view>
        <view class="controls-view">
          <!-- 输入消息时展示 -->
          <view class="input-fixed f fac p-20" v-show="inputIsShow">
            <view class="f1 mr_20">
              <u--input
                confirmType="send"
                shape="circle"
                placeholder="和主播说点什么..."
                border="surround"
                v-model="textValue"
                :focus="isFocus"
                @confirm="sendFn"
                @blur="blurFn"
              ></u--input>
            </view>
            <view>
              <u-button
                type="error"
                size="small"
                text="发送"
                @tap="sendFn"
              ></u-button>
            </view>
          </view>
          <view class="controls-row f fac fjsb p-20" v-show="!inputIsShow">
            <view class="spurious-input mr_20" @click="openInput">
              和主播说点什么...
            </view>
            <!-- <view class="controls-icon-view mr_24">
              <u-icon
                name="more-dot-fill"
                size="50rpx"
                color="#ffffff"
              ></u-icon>
            </view> -->
            <view class="f fac">
              <view class="controls-icon-view relative mr_24">
                <view class="like-num-view">
                  {{ infoData.like_num > 999 ? '999+' : infoData.like_num }}
                </view>
                <u-icon
                  @click="updateLike(null)"
                  name="heart-fill"
                  size="50rpx"
                  :color="likeStatus === 0 ? '#ffffff' : '#f14e4e'"
                ></u-icon>
              </view>
              <view class="controls-icon-view mr_24" @click="shareShow = true">
                <view class="iconfont icon-zb_all_share"></view>
              </view>
              <view class="product-bg-img">
                <text class="product-num" @click="popupIsShow = !popupIsShow">
                  {{
                    infoData.share_live_room_products.length > 99
                      ? '99+'
                      : infoData.share_live_room_products.length
                  }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </div>
    <!-- 商品列表弹出层 -->
    <u-popup
      :show="popupIsShow"
      mode="bottom"
      :round="30"
      @close="popupIsShow = !popupIsShow"
    >
      <view class="product-list-box p-20 gray-bg">
        <view
          v-for="(item, index) in infoData.share_live_room_products"
          :key="index"
          class="mb_10 product-item f"
        >
          <!-- 商品图片 -->
          <view class="product-img mr_16">
            <u-image
              radius="20rpx"
              width="180rpx"
              height="180rpx"
              :src="item.product.image_url"
            ></u-image>
            <view class="product-sort">
              <text>{{ index + 1 }}</text>
            </view>
          </view>
          <!-- 商品信息 -->
          <view class="product-info f-c-sa f1">
            <!-- 标题 -->
            <view class="product-title ell">
              <text>{{ item.product.title }}</text>
            </view>
            <view class="f fac fjsb">
              <view>
                <text class="c-f14d4d">
                  ￥{{ toYuan(item.product.shop_price) }}
                </text>
                <text class="line-through c-regular original-price">
                  ￥{{ toYuan(item.product.origin_price) }}
                </text>
              </view>
              <view class="buy-btn" @click="jumpProductDetail(item.product_id)">
                购买
              </view>
            </view>
          </view>
        </view>
      </view>
    </u-popup>
    <!-- 海报自定义分享语 -->
    <u-popup
      :show="shareShow"
      mode="bottom"
      :round="30"
      @close="shareShow = !shareShow"
    >
      <view class="share-title text-c">自定义分享语</view>
      <view class="pl_30 pr_30">
        <u--textarea
          height="150"
          v-model="posterValue"
          placeholder="请输入"
          count
          maxlength="40"
        ></u--textarea>
        <view class="poster-btn mt_41 mb_20" @click="getPoster">生成海报</view>
      </view>
    </u-popup>
  </view>
</template>
<script>
// 动态引入xgplayer，减少主包大小
// import Player from 'xgplayer'
// import FlvPlayer from 'xgplayer-hls'
/* import 'xgplayer/dist/xgplayer.css'
import 'xgplayer/dist/index.min.css' */
import { IMmixin } from '@/utils/IMMixin.js'
import { Base64 } from 'js-base64'
export default {
  mixins: [IMmixin],
  data() {
    return {
      posterValue: '', // 自定义分享语
      shareShow: false,
      popupIsShow: false,
      likeStatus: 0,
      isFocus: false,
      inputIsShow: false,
      scrollTop: 0,
      textValue: '',
      userSig: '',
      // 参数数据
      paramsData: {
        id: null, // 直播间id
        sid: null, // 小商店店主id
        token: '',
      },
      // 接口返回数据
      infoData: {},
      isWebView: false,
      flvPlayer: null,
      topStyle: {
        left: 10,
        top: 10,
      },
    }
  },
  onLoad(params) {
    if (!params.query) {
      this.toast('系统错误')
      return
    }
    let deQuery = JSON.parse(Base64.decode(params.query))
    this.paramsData.id = parseInt(deQuery.id)
    this.paramsData.sid = parseInt(deQuery.sid)
    this.paramsData.token = deQuery.token
    const systemInfo = uni.getSystemInfoSync()
    this.isWebView = systemInfo && systemInfo.platform === 'h5'
  },
  destroyed() {
    this.imLogout()
  },
  mounted() {
    this.init()
    // 初始化IM
    this.imCreate()
  },
  methods: {
    // 跳转商品详情
    jumpProductDetail(product_id) {
      jweixin.miniProgram.navigateTo({
        url:
          '/packageA/goodsDetail/goodsDetail?goods_id=' +
          product_id +
          '&share_live_room_id=' +
          this.paramsData.id,
      })
    },
    // 获取海报
    async getPoster() {
      let params = {
        sid: this.paramsData.sid,
        share_live_room_id: this.paramsData.id, // 直播间id
        text: this.posterValue, // 自定义分享语
      }
      const { code, data } = await this.post(
        '/api/smallShop/shareLive/getShaveLiveRoomPoster',
        params,
        true,
        true,
        true,
        this.paramsData.token,
      )
      if (code === 0) {
        this.shareShow = false
        let currentTime = Date.now()
        uni.previewImage({
          urls: [data + '?timestamp=' + currentTime],
        })
      }
    },
    // 跳转/通信小商店uniapp页面
    //testSendMessage() {
    // jweixin.miniProgram.postMessage({ data: { foo: "123123" } });
    /* jweixin.miniProgram.navigateTo({
        url:"/packageA/goodsDetail/goodsDetail?goods_id=23"
      }) */
    // jweixin.miniProgram.navigateBack()
    //},

    // inpu失去焦点 为空时改变UI
    blurFn() {
      if (this.textValue === '') {
        this.hidenInput()
      }
    },
    // 发送消息,处理input UI
    sendFn() {
      this.hidenInput()
      this.sendMeaageFn()
    },
    // 打开input框
    openInput() {
      this.inputIsShow = true
      this.isFocus = true
    },
    // 关闭Input
    hidenInput() {
      this.inputIsShow = false
      this.isFocus = false
    },
    // 初始化
    async init() {
      let params = {
        id: this.paramsData.id,
        sid: this.paramsData.sid,
      }
      const { data } = await this.get(
        '/api/shareLive/getShareLiveRoomByIdAndSmallShopId',
        params,
        true,
        false,
        true,
        this.paramsData.token,
      )
      this.infoData = uni.$u.deepClone(data)
      // 直播人数更新
      this.updateViewership()

      console.log('返回数据:', data)
      // 初始化播放器
      this.$nextTick(() => {
        this.initVideo()
      })
      this.timData.groupID = this.infoData.group_id
      // 获取im配置
      let IMSettingRes = await this.get(
        '/api/smallShop/shareLive/getSysShareLiveSetting',
        {},
        false,
        false,
        true,
        this.paramsData.token,
      )
      if (IMSettingRes.code === 0) {
        this.timData.SDKAppID = IMSettingRes.data.sdkappid
        this.timData.secretKey = IMSettingRes.data.im_app_secret
      }
      let sigParams = {
        share_live_room_id: this.paramsData.id, // 直播间id
        uid: this.infoData.user.nickname || this.infoData.user.username, // 店主id
        sid: this.paramsData.sid,
      }
      let sigRes = await this.post(
        '/api/smallShop/shareLive/genSig',
        sigParams,
        false,
        false,
        true,
        this.paramsData.token,
      )
      if (sigRes.code == 0) {
        this.userSig = sigRes.data.data.user_sig
      }
      // 登录IM
      this.imLogin(sigRes.data.user_id, this.userSig)
      this.inquireLikeStatus()
    },
    inquireLikeStatus() {
      this.post(
        '/api/smallShop/shareLive/GetLike',
        { share_live_room_id: this.paramsData.id },
        false,
        false,
        true,
        this.paramsData.token,
      ).then(({ data }) => {
        this.updateLike(data.is_like)
      })
    },
    // 更新点赞状态
    updateLike(status = null) {
      if (status === null) {
        this.post(
          '/api/smallShop/shareLive/saveLikeNum',
          { share_live_room_id: this.paramsData.id },
          false,
          false,
          true,
          this.paramsData.token,
        )
        switch (this.likeStatus) {
          case 0:
            this.likeStatus = 1
            this.infoData.like_num += 1
            break
          case 1:
            this.likeStatus = 0
            this.infoData.like_num -= 1
            break
        }
        return
      }
      this.likeStatus = status
    },
    // 直播人数更新 *目前只有增加人数没有减少人数
    updateViewership() {
      this.infoData.total_num += 1
      this.post(
        '/api/smallShop/shareLive/saveTotalNum',
        { share_live_room_id: this.paramsData.id, num: 1 },
        false,
        false,
        true,
        this.paramsData.token,
      )
    },
    // 初始化播放器 - 使用动态导入
    async initVideo() {
      let windowWidth,
        windowHeight = null
      uni.getSystemInfo({
        success: res => {
          windowWidth = res.windowWidth
          windowHeight = res.windowHeight
        },
      })

      try {
        // 动态导入xgplayer，只在需要时加载
        const [{ default: Player }, { default: FlvPlayer }] = await Promise.all(
          [import('xgplayer'), import('xgplayer-hls')],
        )

        let player = new Player({
          id: 'myVideo',
          width: windowWidth,
          height: windowHeight,
          isLive: true, // 是否是直播，
          plugins: [FlvPlayer], // 视频播放的方式
          url: this.infoData.play_url.play_url_hls, // 地址
          autoplay: true, //
          autoplayMuted: true,
          lang: 'zh-cn', // 是否自动播放，
          screenShot: false, //是否使用截图插件
          rotate: false, //是否使用旋转插件
          download: false, //是否使下载按钮
          pip: false, //是否使用画中画插件
          mini: false, //是否启用mini小窗插件
          controls: false,
        })
        console.log(player, '????')
      } catch (error) {
        console.error('加载播放器失败:', error)
        uni.showToast({
          title: '播放器加载失败',
          icon: 'none',
        })
      }
    },
  },
}
</script>
<style lang="scss" scoped>
#liveItem {
  width: 100vw !important;
  height: 100vh !important;
}
.top-view {
  z-index: 99;
  .info-view {
    height: 80rpx;
    border-radius: 54rpx;
    background-color: rgba($color: #000000, $alpha: 0.3);
    .anchor-name {
      color: #ffffff;
      font-size: 26rpx;
    }
    .viewership {
      font-size: 18rpx;
      color: #e7e7e7;
    }
  }
}
.video-main-div {
  position: relative;
  width: 100vw;
  height: 100vh;
  .bottom-view {
    position: absolute;
    width: 100vw;
    height: 530rpx;
    left: 0;
    bottom: 0;
    z-index: 9;
    color: #ffffff;
    .message-list-view {
      height: 400rpx;
      overflow-y: hidden;
      .message-row {
        margin-bottom: 10rpx;
        .message-view {
          display: inline-block;
          background-color: rgba($color: #000000, $alpha: 0.3);
          border-radius: 20rpx;
          padding: 15rpx 10rpx;
          .user-name-text {
            display: inline-block;
            margin-right: 10rpx;
            color: #cccccc;
          }
        }
      }
    }
    .controls-view {
      .input-fixed {
        background-color: #ffffff;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
      }
      .controls-row {
        .spurious-input {
          // width: 268rpx;
          width: 400rpx;
          padding: 20rpx;
          color: #cccccc;
          border-radius: 40rpx;
          background-color: rgba($color: #000000, $alpha: 0.3);
        }
        .controls-icon-view {
          width: 72rpx;
          height: 72rpx;
          border-radius: 50%;
          background-color: rgba($color: #000000, $alpha: 0.3);
          display: flex;
          align-items: center;
          justify-content: center;
          .icon-zb_all_share {
            font-size: 50rpx;
          }

          .like-num-view {
            position: absolute;
            width: 53rpx;
            height: 24rpx;
            line-height: 24rpx;
            text-align: center;
            background-color: rgba($color: #ff3030, $alpha: 0.8);
            border-radius: 50rpx;
            font-size: 18rpx;
            top: -16rpx;
            left: 12rpx;
          }
        }
        .product-bg-img {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 64rpx;
          height: 74rpx;
          background-image: url('https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/202434/1709534104购物袋.png');
          background-repeat: no-repeat;
          background-size: 100% 100%;
          .product-num {
            display: inline-block;
            margin-top: 30rpx;
            color: #feddc1;
          }
        }
      }
    }
  }
}
.product-list-box {
  max-height: 45vh;
  overflow-y: auto;
  .product-item {
    border-radius: 20rpx;
    background-color: #ffffff;
    padding: 16rpx;
    .product-img {
      position: relative;
      .product-sort {
        position: absolute;
        top: 0;
        left: 0;
        border-top-left-radius: 20rpx;
        border-bottom-right-radius: 20rpx;
        padding: 8rpx 23rpx;
        background-color: rgba($color: #000000, $alpha: 0.7);
        color: #ffffff;
      }
    }
    .product-info {
      .original-price {
        display: inline-block;
        margin-left: 8rpx;
        font-size: 22rpx;
      }
      .buy-btn {
        padding: 14rpx 32rpx;
        border-radius: 32rpx;
        background-color: #f15353;
        color: #ffffff;
      }
    }
  }
}
::v-deep #myVideo {
  .trigger {
    display: none !important;
  }
  .xgplayer-start {
    display: none !important;
  }
}
.relative {
  position: relative;
}
// 灰色背景
.gray-bg {
  background: #f5f5f5;
}

// 自定义分享语部分
.share-title {
  font-weight: bold;
  margin-top: 38rpx;
  margin-bottom: 41rpx;
  font-size: 32rpx;
}
// 生成海报按钮
.poster-btn {
  text-align: center;
  background-color: #f15353;
  border-radius: 40rpx;
  padding: 26rpx 0;
  color: #ffffff;
  font-size: 28rpx;
}
</style>
