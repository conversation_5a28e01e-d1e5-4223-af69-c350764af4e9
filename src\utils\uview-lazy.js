// uview组件按需懒加载配置
// 只在需要时加载对应的组件，减少初始包大小

import Vue from 'vue'
import { lazyLoadComponent } from './lazy-load.js'

// 组件映射表 - 只包含实际使用的组件
const componentMap = {
  // 基础组件
  'u-button': () => import('uview-ui/components/u-button/u-button.vue'),
  'u-input': () => import('uview-ui/components/u-input/u-input.vue'),
  'u-icon': () => import('uview-ui/components/u-icon/u-icon.vue'),
  'u-image': () => import('uview-ui/components/u-image/u-image.vue'),
  'u-text': () => import('uview-ui/components/u-text/u-text.vue'),
  
  // 弹窗组件
  'u-popup': () => import('uview-ui/components/u-popup/u-popup.vue'),
  'u-modal': () => import('uview-ui/components/u-modal/u-modal.vue'),
  'u-toast': () => import('uview-ui/components/u-toast/u-toast.vue'),
  'u-loading': () => import('uview-ui/components/u-loading/u-loading.vue'),
  
  // 导航组件
  'u-tabs': () => import('uview-ui/components/u-tabs/u-tabs.vue'),
  'u-tabs-item': () => import('uview-ui/components/u-tabs-item/u-tabs-item.vue'),
  'u-navbar': () => import('uview-ui/components/u-navbar/u-navbar.vue'),
  'u-tabbar': () => import('uview-ui/components/u-tabbar/u-tabbar.vue'),
  'u-tabbar-item': () => import('uview-ui/components/u-tabbar-item/u-tabbar-item.vue'),
  
  // 表单组件
  'u-form': () => import('uview-ui/components/u-form/u-form.vue'),
  'u-form-item': () => import('uview-ui/components/u-form-item/u-form-item.vue'),
  'u-textarea': () => import('uview-ui/components/u-textarea/u-textarea.vue'),
  'u-picker': () => import('uview-ui/components/u-picker/u-picker.vue'),
  'u-datetime-picker': () => import('uview-ui/components/u-datetime-picker/u-datetime-picker.vue'),
  'u-switch': () => import('uview-ui/components/u-switch/u-switch.vue'),
  'u-checkbox': () => import('uview-ui/components/u-checkbox/u-checkbox.vue'),
  'u-checkbox-group': () => import('uview-ui/components/u-checkbox-group/u-checkbox-group.vue'),
  'u-radio': () => import('uview-ui/components/u-radio/u-radio.vue'),
  'u-radio-group': () => import('uview-ui/components/u-radio-group/u-radio-group.vue'),
  'u-upload': () => import('uview-ui/components/u-upload/u-upload.vue'),
  
  // 展示组件
  'u-swiper': () => import('uview-ui/components/u-swiper/u-swiper.vue'),
  'u-cell': () => import('uview-ui/components/u-cell/u-cell.vue'),
  'u-cell-group': () => import('uview-ui/components/u-cell-group/u-cell-group.vue'),
  'u-list': () => import('uview-ui/components/u-list/u-list.vue'),
  'u-list-item': () => import('uview-ui/components/u-list-item/u-list-item.vue'),
  'u-avatar': () => import('uview-ui/components/u-avatar/u-avatar.vue'),
  'u-badge': () => import('uview-ui/components/u-badge/u-badge.vue'),
  'u-tag': () => import('uview-ui/components/u-tag/u-tag.vue'),
  'u-progress': () => import('uview-ui/components/u-progress/u-progress.vue'),
  'u-circle-progress': () => import('uview-ui/components/u-circle-progress/u-circle-progress.vue'),
  'u-rate': () => import('uview-ui/components/u-rate/u-rate.vue'),
  
  // 反馈组件
  'u-action-sheet': () => import('uview-ui/components/u-action-sheet/u-action-sheet.vue'),
  'u-loadmore': () => import('uview-ui/components/u-loadmore/u-loadmore.vue'),
  'u-empty': () => import('uview-ui/components/u-empty/u-empty.vue'),
  'u-notice-bar': () => import('uview-ui/components/u-notice-bar/u-notice-bar.vue'),
  
  // 布局组件
  'u-divider': () => import('uview-ui/components/u-divider/u-divider.vue'),
  'u-gap': () => import('uview-ui/components/u-gap/u-gap.vue'),
  'u-safe-bottom': () => import('uview-ui/components/u-safe-bottom/u-safe-bottom.vue'),
  'u-status-bar': () => import('uview-ui/components/u-status-bar/u-status-bar.vue'),
  'u-back-top': () => import('uview-ui/components/u-back-top/u-back-top.vue'),
  'u-sticky': () => import('uview-ui/components/u-sticky/u-sticky.vue'),
  'u-transition': () => import('uview-ui/components/u-transition/u-transition.vue'),
  'u-overlay': () => import('uview-ui/components/u-overlay/u-overlay.vue'),
  
  // 其他组件
  'u-count-down': () => import('uview-ui/components/u-count-down/u-count-down.vue'),
  'u-count-to': () => import('uview-ui/components/u-count-to/u-count-to.vue'),
  'u-skeleton': () => import('uview-ui/components/u-skeleton/u-skeleton.vue')
}

// 已注册的组件缓存
const registeredComponents = new Set()

/**
 * 懒加载注册uview组件
 * @param {String} componentName 组件名称
 */
export function lazyRegisterUviewComponent(componentName) {
  if (registeredComponents.has(componentName)) {
    return Promise.resolve()
  }
  
  const importFunc = componentMap[componentName]
  if (!importFunc) {
    console.warn(`Component ${componentName} not found in componentMap`)
    return Promise.reject(new Error(`Component ${componentName} not found`))
  }
  
  return importFunc().then(component => {
    Vue.component(componentName, component.default || component)
    registeredComponents.add(componentName)
    return component
  }).catch(error => {
    console.error(`Failed to load component ${componentName}:`, error)
    throw error
  })
}

/**
 * 批量懒加载注册组件
 * @param {Array} componentNames 组件名称数组
 */
export function lazyRegisterUviewComponents(componentNames) {
  return Promise.all(
    componentNames.map(name => lazyRegisterUviewComponent(name))
  )
}

/**
 * 预加载常用组件
 */
export function preloadCommonComponents() {
  const commonComponents = [
    'u-button',
    'u-input',
    'u-icon',
    'u-image',
    'u-text',
    'u-popup',
    'u-loading'
  ]
  
  // 使用 requestIdleCallback 在空闲时预加载
  if (typeof requestIdleCallback !== 'undefined') {
    requestIdleCallback(() => {
      lazyRegisterUviewComponents(commonComponents)
    })
  } else {
    // 降级处理
    setTimeout(() => {
      lazyRegisterUviewComponents(commonComponents)
    }, 2000)
  }
}

/**
 * 根据页面需求加载组件
 * @param {String} pageName 页面名称
 */
export function loadComponentsForPage(pageName) {
  const pageComponentMap = {
    'index': ['u-swiper', 'u-tabs', 'u-tabs-item', 'u-image', 'u-button'],
    'classify': ['u-tabs', 'u-tabs-item', 'u-list', 'u-list-item'],
    'shoping_car': ['u-checkbox', 'u-checkbox-group', 'u-button', 'u-image'],
    'promotion': ['u-image', 'u-button', 'u-popup', 'u-tabs'],
    'membercenter': ['u-cell', 'u-cell-group', 'u-avatar', 'u-badge'],
    'login': ['u-form', 'u-form-item', 'u-input', 'u-button'],
    'register': ['u-form', 'u-form-item', 'u-input', 'u-button', 'u-checkbox']
  }
  
  const components = pageComponentMap[pageName] || []
  if (components.length > 0) {
    return lazyRegisterUviewComponents(components)
  }
  
  return Promise.resolve()
}

// 引入uview的JS工具函数
let $u = null
export async function loadUviewUtils() {
  if (!$u) {
    const uviewModule = await import('uview-ui')
    $u = uviewModule.$u
    Vue.prototype.$u = $u
  }
  return $u
}

export default {
  lazyRegisterUviewComponent,
  lazyRegisterUviewComponents,
  preloadCommonComponents,
  loadComponentsForPage,
  loadUviewUtils
}
