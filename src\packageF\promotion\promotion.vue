<!-- 推广 -->
<template>
	<view>
		<view v-if="!checkNull(loginUser)">
			<unloggedPage></unloggedPage>
		</view>
		<view v-else>
			<view class="ggbg"></view>
			<view style="margin-top: -400rpx;">
				<view class="p-20 f fac fjsb" style="color: #ffffff;">
					<!-- 头像 名称 ID -->
					<view class="d-bf">
						<view class="d-f">
							<view>
								<u-avatar :src="user.avatar" size="90">
								</u-avatar>
							</view>
							<view class="ml-20">
								<view class="fw-b fs-3 d-f fac">
									<view>
										{{ user.nickname == '' ? '普通用户' : user.nickname }}
									</view>
									<view class="ml_15">
										(ID:{{ user.id }})
									</view>
								</view>
								<view class="mt-15 fs-1 user-level-view">{{ user.level.name }}</view>
							</view>
						</view>
					</view>
					<view class="connection-view" @click="openConnection">
						<view class="iconfont icon-wodekefu"></view>
						<view class="text">客服</view>
					</view>
				</view>
				<!-- 邀请码 邀请链接 -->
				<view class="bg-white m-20 radius15" style="margin-top: 35rpx;" v-if="IsAgent">
					<view class="p-20 pb-30 radius15 rw-gradation">
						<view class="d-f">
							<view class="d-cc">我的邀请码：{{ user.invite_code }}</view>
							<view class="ml-10 d-cc copyCss" @click="myCopy('invite_code')">复制</view>
						</view>
						<view class="d-f mt_18">
							<view style="width: 550rpx;line-height:40rpx">我的邀请链接：{{ user.shareDddress }}</view>
							<view class="ml-10 d-cc copyCss" @click="myCopy('shareDddress')">复制</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 收入客户详情 -->
			<view class="flow d-f bg-white radius15 m-20 pb_29 pl_30" :class="IsAgent ? '' : 'mt_1'">
				<view v-for="(item, index) in earningsFigures" :key="index" class="mr-25 mt-30" style="width: 200rpx;">
					<view class="fw-b d-cc" style="font-size: 30rpx;" v-if="item.clientType" @click="navTo(item.url)">
						{{ item.value || '0' }}
					</view>
					<view class="fw-b d-cc" style="font-size: 30rpx;" v-else @click="navTo(item.url)">
						{{ item.value | processing }}
					</view>
					<view class="d-cc" style="transform: scale(0.75);color: #5d5d5d;">{{ item.name }}</view>
				</view>
				<view style="width: 228rpx;"></view>
				<view class="d-cc-c mt-20 ml-30" @click="navTo('/packageB/member/withdraw?withdrawal_type=2')">
					<view class="withdraw-btn">去提现</view>
				</view>
			</view>
			<view class="m-20">
				<u-tabs :list="tabsList" @change="handleTabChange" :current="current"
					:activeStyle="{ color: '#F15353', fontWeight: '700' }" lineColor="#F15353" lineWidth="26rpx"
					lineHeight="6rpx"></u-tabs>
				<view class="mt_20">
					<view class="bg-white mb_20 p-20 f fac fjsb radius15">
						<view>
							<template v-if="listLevelName">
								当前等级: {{ listLevelName }}
							</template>
						</view>
						<view style="color: #6E6E79">
							已结算: ￥{{ toYuan(listAmountTotal) }}
						</view>
					</view>
					<view v-if="key == 42 && myAgencyTrue" class="bg-white mb_20 p-25 f fac fjsb radius15"
						@click="distributorsAndShops">
						<view class="fs-2 f-bold">团队分销商和小商店</view>
						<view class="fs-3 ml-5 iconfont icon-member_right" style="color: #6E6E79"></view>
					</view>
					<view v-if="key == 2 && myShopTrue" class="bg-white mb_20 p-25 f fac fjsb radius15" @click="myShop">
						<view class="fs-2 f-bold">我的小商店</view>
						<view class="fs-3 ml-5 iconfont icon-member_right" style="color: #6E6E79"></view>
					</view>
					<view class="bg-white mb_20 p-20 radius15">
						<view class="f fac">
							<view class="f1 mr_10">
								<u-input placeholder="订单号" clearable v-model="searchInfo.order_sn"></u-input>
							</view>
							<view class="f1 ml_10">
								<u-input placeholder="下单会员手机号" clearable v-model="searchInfo.member"></u-input>
							</view>
						</view>
						<view class="f fac mt_20 mb_10">
							<view class="f1 mr_10 select-view" @click="settleStatusShow = !settleStatusShow">
								<view class="f fac fjsb" style="padding: 12rpx 18rpx">
									{{ settleTitle }}
									<u-icon class="ml_10 mr_5" color="#AAAAB3" name="arrow-down-fill"
										size="22"></u-icon>
								</view>
							</view>
							<view class="f1 ml_10">
								<view class="searchBtn" @click="search">搜索</view>
							</view>
						</view>
					</view>
					<promotionList v-if="list.length" :isLast="isLast" :list="list" :status="tabsList[current].key">
					</promotionList>
					<u-empty v-if="total === 0" mode="list" text="暂无数据" iconSize="200" textSize="24">
					</u-empty>
				</view>
			</view>
			<!-- 结算状态筛选弹框 -->
			<u-picker :show="settleStatusShow" :columns="columns" keyName="label" confirmColor="#F15353"
				@confirm="handleSettleConfirm" @cancel="settleStatusShow = false"></u-picker>
			<!-- 推荐人弹窗 -->
			<view>
				<u-popup :show="referrerShow" @close="referrerShow = false" mode="center" :round="15">
					<view style="width: 500rpx;">
						<view class="d-bf pr-20 mt-20">
							<view class="fs-1-5" style="margin-left: 180rpx;">我的推荐人</view>
							<view class="iconfont icon-guanbi d-cc fs-2-5" @click="referrerShow = false">
							</view>
						</view>
						<view class="d-f mt-45 ml-40 mb-100">
							<u--image :src="referrer.avatar" shape="circle" width="70" height="70"></u--image>
							<view class="ml-20 fs-1-5 fw-b">
								<view>昵称:{{ referrer.nickname }}</view>
								<view class="mt-15">手机号:{{ referrer.username }}</view>
								<view class="mt-15">邀请码:{{ referrer.invite_code }}</view>
							</view>
						</view>
					</view>
				</u-popup>
			</view>
		</view>
		<u-popup bgColor="transparent" :show="isShow" mode="center">
			<view class="qr-code-view">
				<view class="wx-bg">
					<view class="title pt_48 ml_41">客服微信</view>
					<view class="code-view">
						<view class="f fjc mt_90">
							<u--image :src="qr_code_url" width="240rpx" height="240rpx"></u--image>
						</view>
						<view class="text-c c-7e7e7e font_size12 mt_60">
							<view>长按识别保存二维码，</view>
							<view>添加后咨询商品、订单等事宜！</view>
						</view>
					</view>
				</view>
				<view class="close-icon-view f fac fjc" @click="
			isShow = false;
		qr_code_url = '';
		">
					<u-icon name="close-circle" color="#ffffff" size="60"></u-icon>
				</view>
			</view>
		</u-popup>
		<myTabBar ref="myTabBar"></myTabBar>
	</view>
</template>

<script>
import unloggedPage from '@/common/unloggedPage/unloggedPage.vue'
import myTabBar from "../../components/tabbar.vue";
import promotionList from "@/components/promotionList.vue";
export default {
	components: {
		unloggedPage,
		myTabBar,
		promotionList
	},
	data() {
		return {
			isShow: false,
			qr_code_url: '',
			// 是否为最后一页
			isLast: true,
			settleTitle: "结算状态",
			settleStatusShow: false,
			columns: [
				[
					{
						type: 1,
						label: "未结算"
					},
					{
						type: 2,
						label: "已结算"
					},
					{
						type: -1,
						label: "已失效"
					},
					{
						type: 3,
						label: "子商户"
					}
				]
			],
			current: 0,
			tabsList: [],
			list: [],
			listAmountTotal: 0,
			listLevelName: "",
			page: 1,
			pageSize: 10,
			total: 0,
			searchInfo: {
				// order_sn: null, // int
				// member: "", // string
				/**
				 * int
				 * 1 = 未结算
				 * 2 = 已结算
				 * -1 = 已失效
				 */
				// settle_status: null
			},
			referrerShow: false, //推荐人弹窗是否弹出
			parent_id: '', //上级id
			IsAgent: false, //是否有邀请权限
			user: {}, //当前用户信息
			loginUser: '',
			referrer: {}, //推荐人信息
			type: { //选项卡值切换
				curve: 'dynamic',
				curveCurNow: 0,
				jurisdiction: 'open',
				jurisdictionCurveCurNow: 0,
			},
			isApply: 0,
			status: 0,//设置代理不同状态
			distributionLevelData: [], //等级列表
			earningsFigures: [ //收益数据
				{
					name: '累计收入',
					url: '/packageB/wallet/wallet?statur=1'
				},
				{
					name: '已提现收入',
					url: '/packageB/wallet/wallet?statur=2'
				},
				{
					name: '未提现收入',
					url: '/packageB/wallet/wallet?statur=3'
				},
				{
					name: '提现中的收入',
					url: '/packageB/wallet/wallet?statur=4'
				},
				{
					name: '累计手续费',
					url: '/packageB/wallet/wallet'
				},
				{
					name: '累计劳务费',
					url: '/packageB/wallet/wallet'
				},
				{
					name: '我的客户',
					clientType: 1,
					url: '/packageB/user/myCustomer/myCustomer'
				}
			],
			key: 0,
			currtrue:false, // 进入页面是否优先展示代理
			loginUserTrue:false,
			pid:0,
			myShopTrue:false,// 分销是否显示小商店
			myAgencyTrue:false,// 代理是否显示小商店和分销
			promotionOptions: '', // 页面路径携带的参数
		}
	},
	onLoad(e) {
		this.sm(e) // 扫码进入判断函数
	},
	onHide() {
		this.distributionLevelData = []
	},
	onShow() {
		setTimeout(() => {
			this.$refs.myTabBar.tabBarActive = uni.getStorageSync('tabBarActive')
		}, 500)
		
		this.getUser()
		this.getAgentInfo()
		this.getTabsList()
		this.loginUser = uni.getStorageSync('user')
		if (this.loginUserTrue && !this.checkNull(this.loginUser)) {
			uni.setStorageSync('invite_code', this.pid)
			this.navTo(`/pages/login/login`) // 扫码判断是否为登入状态
		}
	},
	filters: {
		processing(price) {
			return price ? price : '0.00';
		}
	},
	computed: {
	},
	onReachBottom() {
		if (this.list.length < this.total) {
			this.page += 1
			this.fetch()
		}
	},
	methods: {
		sm(e) {
			this.promotionOptions = e
			if (e.pid) {
				// 获取用户的扫码获取用户的pid
				this.pid = e.pid
				this.loginUserTrue = true
				this.post('/api/institution/getIdentity', { identity_type: 1 }).then(async res => {
					if (res.code === 0) {
						// 判断有无代理身份
						if (res.data.identity_res) {
							// 有身份则展示代理
							this.currtrue = true
							// this.navTo('/pages/promotion/promotion')
						} else {
							// 无身份跳转到付款升级
							// 获取订单id 跳转到订单页面
							
							let i = await this.post('/api/institution/purchase')
							if (i.code === 0) {
								uni.setStorageSync('orderIds', i.data.order_ids);
								uni.setStorageSync('pageSourse', '');
								this.navTo('/packageD/agencies/distributorPayment')
							}
						}
					}
				})
			}
		},
		async openConnection() {
			const { data } = await this.get("/api/home/<USER>", {}, true)
			this.qr_code_url = data.footer.service_code
			this.isShow = true
		},
		// 选中结算状态
		handleSettleConfirm(e) {
			this.settleTitle = e.value[0].label;
			this.searchInfo.settle_status = e.value[0].type
			this.settleStatusShow = false
		},
		// tabs切换
		handleTabChange(val) {
			if (val.key === 2) {
				this.myShopTrue = val.team_show
			}
			if (val.key === 42) {
				this.myAgencyTrue = val.team_show
			}
			this.current = val.index
			this.key = val.key
			this.page = 1;
			this.searchInfo = {}
			this.fetch()
		},
		// 获取tabs
		getTabsList() {
			this.get("/api/promotion/getColumns").then(res => {
				/**
				 * 1 = 区域分红
				 * 2 = 分销分成
				 * 3 = 招商分红
				 * 14 = cps分成
				 * 17 = 小商店分成
				 * 18 = 课程分红
				 * 20 = 聚推联盟分成
				 * 23 = 电影票分红
				 * 38 = 美团分销分成
				 * 42 = 代理
				 * 44 = 本地生活品牌分成
				 * 45 = 本地生活门店分成
				 */
				
				
				let that = this
				this.tabsList = Object.keys(res.data.columns).map(function (key) {
					// 判断是否显示分销小商店
					if (key == 2) {
						that.myShopTrue = res.data.columns[key].team_show
					}
					// // 判断是否显示代理的小商店和分销
					if (key == 42) {
			        	that.myAgencyTrue = res.data.columns[key].team_show
			        }
					return res.data.columns[key]
				})
				this.tabsList = this.tabsList.sort((a,b) => b.sort - a.sort)
				// 扫码代理有身份则默认展示代理
				if (this.currtrue) {
					let s = this.tabsList.findIndex(item => item.key === 42)
					if (s) {
						this.current = s
					}else{
						this.current = 0
					}
				}
				// 扫码分销有身份则默认展示分销
				if (uni.getStorageSync('distri') === 'distri') {
					uni.setStorageSync('distri', '');
					let s = this.tabsList.findIndex(item => item.key === 2)
					if (s) {
						this.current = s
					}else{
						this.current = 0
					}
				}
				// 判断是否跳转到本地生活品牌分成
				if (this.promotionOptions.localLife === '44') {
					let s = this.tabsList.findIndex(item => item.key === 44)
					if (s) {
						this.current = s
					}else{
						this.current = 0
					}
				}
				// 判断是否跳转到本地生活门店分成
				if (this.promotionOptions.localLife === '45') {
					let s = this.tabsList.findIndex(item => item.key === 45)
					if (s) {
						this.current = s
					}else{
						this.current = 0
					}
				}
				this.key = this.tabsList[this.current].key
				this.fetch()
			})
		},
		search() {
			this.list = []
			this.fetch(1)
		},
		// 获取列表
		fetch(page = this.page, pageSize = this.pageSize) {
			this.get('/api/' + this.tabsList[this.current].url, { page, pageSize, ...this.searchInfo }, true).then(res => {
				this.total = res.data.total
				this.listAmountTotal = res.data.amount_total
				this.listLevelName = res.data.level_name
				if (this.page === 1) {
					this.list = res.data.list
				} else if (!this.page < this.total / this.pageSize) {
					this.list = [...this.list, ...res.data.list]
				}
				this.isLast = this.page < this.total / this.pageSize ? true : false
			})
		},
		curveSectionChange(index) { //收入动态收入占比切换
			if (index == 0) {
				this.type.curve = 'dynamic'
			} else {
				this.type.curve = 'proportion'
			}
			this.type.curveCurNow = index;
		},
		getUser() { //获取最新用户信息
			this.post('/api/center/index', {}, true).then(data => {
				this.user = data.data.user
				//#ifdef H5
				this.user["shareDddress"] = document.location.protocol + "//" + window.location.host + "/?shared=" + this.code.encode(this.user
					.id) //我的邀请链接
				//#endif
				//#ifndef H5
				this.user["shareDddress"] = String(this.api.host).replace('supplyapi', '') + "?shared=" + this.code.encode(this.user
					.id) //我的邀请链接
				//#endif
				this.parent_id = data.data.user.parent_id
				this.getExpand()
				// 获取上级暂时先注释掉
				/* if (!this.parent_id) return
				this.parentMessage(this.parent_id) */
			})
		},
		getExpand() { //获取推广页面数据
			this.get('/api/expand/getExpand', {}, true).then(data => {
				/*收益数据*/
				let expandData = data.data;
				this.$set(this.earningsFigures[0], 'value', this.toYuan(expandData.incomes.total))
				this.$set(this.earningsFigures[1], 'value', this.toYuan(expandData.incomes.have_withdrawal))
				this.$set(this.earningsFigures[2], 'value', this.toYuan(expandData.incomes.no_withdrawal))
				this.$set(this.earningsFigures[3], 'value', this.toYuan(expandData.incomes.withdrawal_of))
				this.$set(this.earningsFigures[4], 'value', this.toYuan(expandData.incomes.poundage_amount_total))
				this.$set(this.earningsFigures[5], 'value', this.toYuan(expandData.incomes.service_tax_total))
				this.$set(this.earningsFigures[6], 'value', expandData.incomes.consumer_total)
				
			})
		},
		getAgentInfo() { //获取是否可以推广与推广信息
			this.post('/api/user/agentInfo', {}, true).then(data => {
				this.IsAgent = data.data.IsAgent
			})
		},
		parentMessage(id) { //上级信息
			let json = {
				pid: id
			}
			this.post('/api/user/getParentInfo', json, true).then(data => {
				this.referrer = data.data
			})
		},
		myCopy(type) { //复制
			let value;
			if (type == "invite_code") {
				value = this.user.invite_code
			} else {
				value = this.user.shareDddress
			}
			// #ifdef H5
			this.$copyText(value).then(
				res => {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					})
				}
			).catch(Error => {
				uni.showToast({
					title: '复制失败',
					icon: 'none'
				})
			})
			// #endif
			// #ifndef H5
			uni.setClipboardData({
				data: value, // e是你要保存的内容
				success: function () {
					uni.showToast({
						title: '复制成功',
						icon: 'none'
					})
				}
			})
			// #endif
		},
		distributorsAndShops() {
			this.navTo("/packageD/agencies/distributorsAndShops")
		},
		myShop() {
			this.navTo("/packageD/agencies/myShop")
		}
	}
}
</script>

<style scoped lang="scss">
.qr-code-view {
	position: relative;
	max-width: 500rpx;

	.close-icon-view {
		width: 100%;
		z-index: 99;
		position: absolute;
		left: 0;
		bottom: -86rpx;
	}
}

.wx-bg {
	width: 500rpx;
	height: 664rpx;
	background-image: url('https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/20231120/1700464059wxbg.png');
	background-size: 100%;
	background-repeat: no-repeat;

	.title {
		font-size: 56rpx;
		font-weight: bold;
		color: #0f0f0f;
	}

	.code-view {
		width: 398rpx;
		height: 362rpx;
		border: 1px solid #ffffff;
		background-color: linear-gradient(180deg, #fff1f1 0%, #ffffff 100%);
		border-radius: 46rpx;
		margin: 0 auto;
		margin-top: 56rpx;
		position: relative;

		.avatar-view {
			position: absolute;
			top: -40rpx;
			left: 0;
			right: 0;
		}
	}
}

.connection-view {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(180deg, #FFD4D4 0%, #FFFFFF 100%);
	border-radius: 50%;
	color: #F15353;
	text-align: center;

	.icon-wodekefu {
		margin-top: 5rpx;
		font-size: 40rpx;
	}

	.text {
		font-size: 24rpx;
	}
}

.searchBtn {
	text-align: center;
	padding: 15rpx 0;
	color: #ffffff;
	background-color: #F15353;
	border-radius: 40rpx;
}

.select-view {
	border-radius: 8rpx;
	border: 1rpx solid #dadbde;
}

// 提现按钮
.withdraw-btn {
	padding: 15rpx 34rpx;
	background-color: #F15353;
	border-radius: 40rpx;
	color: #ffffff;
}

.user-level-view {
	display: inline-block;
	padding: 10rpx 20rpx;
	border-radius: 40rpx;
	background-color: rgba($color: #000000, $alpha: 0.1);
}

.ggbg {
	/* 公告背景*/
	background-image: linear-gradient(#f15353, #f5f5f5);
	width: 100%;
	height: 400rpx;
}

.copyCss {
	width: 110rpx;
	height: 60rpx;
	padding-bottom: 5rpx;
	background-color: rgba($color: #F15353, $alpha: 0.1);
	color: #F15353;
	border-radius: 40rpx;
	transform: scale(0.75);
}

.absoluteCss {
	z-index: 99;
	position: absolute;
	top: 170rpx;
	right: 40rpx;
	font-size: 40rpx;
}

::v-deep .u-subsection__bar {
	background-color: #f14e4e !important;
}

::v-deep .u-subsection {
	padding: 0rpx !important;
	border-radius: 15rpx !important;
	height: 75rpx !important;
	width: 600rpx !important;
}

.rw-gradation {
	background-image: linear-gradient(#ffeaea, #f5f5f5);
}
</style>
