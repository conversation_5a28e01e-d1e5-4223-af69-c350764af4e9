<template>
    <view>
        <view v-for="itemList in list" :key="itemList.id"
            @click="navTo('/packageC/album/albumDetail?goods_id=' + itemList.id)"
            class="m-20 p-20 bsbb radius15 bg-white" style="height: 480rpx;">
            <u--image v-if="itemList.covers !== null && itemList.covers[0].src" radius="16" :showLoading="true" height="330rpx" width="100%"
                :src="itemList.covers[0].src">
            </u--image>
            <u-image v-else width="100%" height="330rpx" radius="16">
                <u-empty text="暂无图片" mode="data"></u-empty>
            </u-image>
            <view class="f fac fjsb">
                <view class="f1">
                    <view class="title mt-25 limit-text-1" style="width: 500rpx;">
                        {{ itemList.name }}</view>
                    <view class="mt-15 fs-1-5 limit-text-1 color80" style="width: 500rpx;">
                        {{ itemList.describe }}</view>
                </view>
                <view class="d-cc-c">
                    <view class="f fac mt-25 pl-10 pr-15 radius20"
                        style="background: linear-gradient( 180deg, #FFFFFF 0%, #FFDEDE 100%);" v-if="user">
                        <u--image :showLoading="true" height="22rpx" width="19rpx"
                            src="https://yxgyl.obs.cn-south-1.myhuaweicloud.com/2025217/1d047a0949a23e3186e5ccec48679dbc">
                        </u--image>
                        <span class="fs-2 fw-b ml-5" style="color: #161616;">{{ itemList.sales_total
                            }}</span>
                    </view>
                    <span class="fs-2 c-orange mt-25" @click.stop="jumpLogin" v-else>仅登录可见</span>
                    <view class="mt-15 fs-0 color80">累计销量</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    props: {
        list: { //商品信息
            type: Array,
            default: () => {
                return []
            },
        }
    },
    data() {
        return {
            user: uni.getStorageSync('user'),
        }
    },
    onShow () {
    },
    methods: {
        jumpLogin() {
            if (this.checkWenxin() && !this.checkNull(this.user)) {
                // 公众号登录
                this.isWeiXinBrowser()
                return;
            }
            if (!this.user) {
                this.navTo("/pages/login/login")
            }
        },
    }
}
</script>

<style scoped>
.limit-text-1 {
    word-break: break-all;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
}

.bsbb {
	box-sizing: border-box;
}
.color80 {
	color: #808080;
}
.title {
	color: #00001C;
	font-weight: bold;
	font-size: 30rpx;
}
</style>