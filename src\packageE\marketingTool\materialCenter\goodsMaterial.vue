<template>
  <view class="box">
    <!-- 搜索 -->
    <!-- <view class="search-box pb_20">
      <u-search
        height="72"
        placeholder="请输入素材标题"
        clearabled
        v-model="keyword"
        :showAction="false"
        searchIconSize="40"
        @change="changeSearch"
      ></u-search>
    </view> -->
    <!-- 分类 -->
    <!-- <view class="category-box">
      <u-tabs
        :list="categoryList"
        keyName="title"
        lineHeight="5"
        lineColor="#2658FA"
        :inactiveStyle="{ color: '#161616', fontSize: '28rpx' }"
        :activeStyle="{ color: '#2658FA', fontSize: '28rpx' }"
        :current="current"
        @click="changeTabs"
      >
        <view slot="right" style="padding-right: 20rpx" @tap="changeCatrgory">
          <u-icon
            v-if="!categoryIsShow"
            name="arrow-down"
            size="40"
            bold
          ></u-icon>
          <u-icon v-else name="arrow-up" size="40" bold></u-icon>
        </view>
      </u-tabs>
      <view class="popup-view" v-if="categoryIsShow">
        <view class="popupCategory2-view f fw">
          <view
            class="item"
            v-for="(item, index) in category2"
            :key="index"
            :class="checkCategory2Id === item.id ? 'active' : ''"
            @click="handleCategory2(item, index)"
          >
            {{ item.title }}
          </view>
        </view>
      </view>
    </view> -->
    <!-- 分类 end -->
    <!-- 列表 -->
    <view v-if="tableList.length">
      <view class="material-box" v-for="item in tableList" :key="item.id">
        <view class="material-item f fac fjsb">
          <view class="f fac pl_25 pt_30">
            <u-image
              :src="item.user.avatar"
              width="80rpx"
              height="80rpx"
              shape="circle"
            ></u-image>
            <view class="ml_15">
              <view class="fs-2-5 blod black">
                {{
                  item.user.nickname
                    ? item.user.nickname
                    : '用户' + item.user.username.slice(-4)
                }}
              </view>
              <view class="mt_10 grey fs-1">
                {{ formatDateTime(item.created_at) }}
              </view>
            </view>
          </view>
          <view class="info-view mr_25" @click="goMaterialCenterDetail(item)">
            详情 >
          </view>
        </view>
        <view class="pl_25 pr_25 mt_40 black blod fs-3">
          {{ item.title }}
        </view>
        <view class="pl_25 pr_25 mt_35 black fs-2-5">
          {{ item.content }}
        </view>
        <view class="pl_25 pr_25 pb_30 mt_40 f fac">
          <view
            v-for="(items, index) in item.img_url_show"
            :key="index"
            class="pr-10"
            @click="downImage(items)"
          >
            <u-image
              :src="items"
              width="210rpx"
              height="210rpx"
              radius="16rpx"
            ></u-image>
          </view>
          <video
            v-if="item.video_url && item.img_url.length < 3"
            style="width: 200rpx; height: 200rpx"
            :src="item.video_url"
            :controls="false"
          />
        </view>
        <u-line></u-line>
        <view>
          <view class="mt_35 f fac">
            <view class="f fac f1 fjc" @click="downloadImgStr(item)">
              <!-- #ifdef MP-WEIXIN -->
              <view class="iconfont icon-fontclass-xiazai"></view>
              <view class="ml_10">下载图文</view>
              <!-- #endif -->
              <!-- #ifdef APP-PLUS || H5 -->
              <view class="iconfont icon-fuzhi"></view>
              <view class="ml_10">复制图文</view>
              <!-- #endif -->
            </view>
            <view class="f fac f1 fjc" @click="getPoster(item)">
              <view class="iconfont icon-material_goodsCode"></view>
              <view class="ml_10">商品码</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="material-empty">
      <img src="../../../static/image/empty.png" />
      <view>暂无相关素材</view>
    </view>
    <view v-if="tableList.length === total && tableList.length !== 0">
      <u-divider textSize="30" text="已经到底了"></u-divider>
    </view>
    <!-- <view class="btn-box">
      <view class="add-box" @click="addMaterial">
        <view class="iconfont icon-fabu"></view>
        <view class="mt_5">发布</view>
      </view>
      <view class="my-box" @click="myMaterial">
        <view class="iconfont icon-geren"></view>
        <view>我的</view>
      </view>
    </view> -->
    <!-- #ifdef MP-WEIXIN -->
    <u-popup :show="imageShow" mode="center" @close="imageShow = false">
      <u--image :src="downImages" width="600rpx" height="960rpx"></u--image>
      <!-- <img
        :src="downImages"
        style="max-width: 600rpx"
        show-menu-by-longpress="1"
      /> -->
    </u-popup>
    <!-- #endif -->
    <!-- #ifdef H5 -->
    <u-popup :show="imageShow" mode="center" @close="imageShow = false">
      <!-- <u--image :src="downImages" width="600rpx" height="960rpx"></u--image> -->
      <img
        :src="downImages"
        style="max-width: 600rpx"
        show-menu-by-longpress="1"
      />
    </u-popup>
    <!-- #endif -->
  </view>
</template>
<script>
export default {
  data() {
    return {
      keyword: '',
      current: 0,
      categoryList: [{ id: 0, title: '全部' }], // 非隐藏分组
      category2: [], // 隐藏分组
      categoryIsShow: false, // 分组隐藏或显示
      checkCategory2Id: null, // 展开时选中的分组
      tableList: [],
      imageShow: false,
      downImages: '',
      page: 1,
      pageSize: 10,
      total: 0,
      product_id: null,
    }
  },
  onLoad(options) {
    this.product_id = options.id
    // this.getGroupList()
    this.getMaterialList()
  },
  async onReachBottom() {
    const data = {
      product_id: parseInt(this.product_id),
      page: ++this.page,
      pageSize: this.pageSize,
    }
    if (this.tableList.length < this.total) {
      const res = await this.post('/api/material/center/list', data)
      if (res.code === 0) {
        res.data.list.forEach(item => {
          if (item.img_url) {
            item.img_url_show = item.img_url.split(',').slice(0, 3)
            item.img_url = item.img_url.split(',')
          } else {
            item.img_url = []
          }
        })
        this.tableList = this.tableList.concat(res.data.list)
      }
    }
  },
  methods: {
    // 获取分组列表
    async getGroupList() {
      const data = {
        title: '',
      }
      const res = await this.post('/api/material/center/group', data)
      if (res.code === 0) {
        res.data.forEach(item => {
          this.categoryList.push(item)
        })
        this.category2 = res.data
      }
    },
    // 切换分组
    changeTabs(item) {
      this.checkCategory2Id = item.id
      this.page = 1
      this.getMaterialList()
    },
    // 修改分组显示
    changeCatrgory() {
      this.categoryIsShow = !this.categoryIsShow
    },
    // 分组展开时选择
    handleCategory2(item, index) {
      this.checkCategory2Id = item.id
      this.current = index + 1
      this.page = 1
      this.getMaterialList()
    },
    // 搜索素材中心列表
    changeSearch() {
      this.page = 1
      this.getMaterialList()
    },
    // 获取素材中心列表
    async getMaterialList() {
      const data = {
        product_id: parseInt(this.product_id),
        page: this.page,
        pageSize: this.pageSize,
      }
      const res = await this.post('/api/material/center/list', data)
      if (res.code === 0) {
        res.data.list.forEach(item => {
          if (item.img_url) {
            item.img_url_show = item.img_url.split(',').slice(0, 3)
            item.img_url = item.img_url.split(',')
          } else {
            item.img_url = []
          }
        })
        this.tableList = res.data.list
        this.total = res.data.total
      }
    },
    // 下载图片
    downImage(item) {
      this.imageShow = true
      this.downImages = item
    },
    // 下载图文
    downloadImgStr(item) {
      // 下载图片部分
      if (item.img_url.lengh !== 0) {
        item.img_url.forEach(url => {
          uni.downloadFile({
            url: url,
            success: res => {
              if (res.statusCode === 200) {
                uni.saveImageToPhotosAlbum({
                  filePath: res.tempFilePath,
                  success: function (img) {
                    console.log(img, '图片下载成功')
                  },
                })
              }
            },
          })
        })
      }
      // 下载视频部分
      if (item.video_url) {
        uni.downloadFile({
          url: item.video_url,
          success: function (res) {
            if (res.statusCode === 200) {
              uni.saveVideoToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function (video) {
                  console.log(video, '图片下载成功')
                },
              })
            }
          },
        })
      }
      uni.setClipboardData({
        data: item.content,
        success: function () {
          console.log('成功复制文字')
        },
      })
    },
    // 跳转详情
    goMaterialCenterDetail(item) {
      this.navTo(
        '/packageE/marketingTool/materialCenter/materialCenterDetail?id=' +
          item.id,
      )
    },
    // 跳转发布素材
    addMaterial() {
      this.navTo('/packageE/marketingTool/materialCenter/addMaterial')
    },
    // 跳转我的素材
    myMaterial() {
      this.navTo('/packageE/marketingTool/materialCenter/myMaterial')
    },
    // 生成海报
    async getPoster(item) {
      let data = {
        id: parseInt(item.product_id),
      }
      /*#ifdef MP-WEIXIN*/
      data.qr = 2
      /*#endif*/
      /*#ifdef H5*/
      data.qr = 1
      /*#endif*/
      const res = await this.post('/api/material/product/poster', data)
      if (res.code === 0) {
        this.imageShow = true
        this.downImages = res.data.link
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.box {
  padding-bottom: 100rpx;
}
.search-box {
  padding-left: 20rpx;
  padding-top: 20rpx;
  padding-right: 20rpx;
  width: 710rpx;
  height: 72rpx;
  background-color: #ffffff;
}
.category-box {
  background-color: #ffffff;
  padding-bottom: 10rpx;
  position: relative;
  .popup-view {
    position: absolute;
    top: 96rpx;
    left: 0;
    width: 100%;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.5);
    z-index: 99;
    .popupCategory2-view {
      max-height: 228rpx;
      padding: 25rpx;
      background-color: #ffffff;
      border-bottom-left-radius: 24rpx;
      border-bottom-right-radius: 24rpx;
      overflow: scroll;
      .item {
        background-color: #f5f5f5;
        border-radius: 16rpx;
        padding: 15rpx 20rpx;
        margin-right: 20rpx;
        margin-bottom: 20rpx;
        &:last-child {
          margin-right: 0;
        }
        &.active {
          background-color: #eef2ff;
          border: 1px solid #2658fa;
          color: #2658fa;
        }
      }
    }
  }
}
.material-box {
  width: 702rpx;
  background-color: #ffffff;
  margin-top: 20rpx;
  border-radius: 20rpx;
  margin-left: 24rpx;
  padding-bottom: 35rpx;
  .material-item {
    .info-view {
      padding: 10rpx 20rpx;
      background-color: rgba($color: #2658fa, $alpha: 0.08);
      border-radius: 30rpx;
      color: #2658fa;
    }
  }
}
.material-empty {
  position: absolute;
  top: 34%;
  left: 34%;
  color: #6e6e79;
  img {
    width: 230rpx;
    height: 230rpx;
  }
  view {
    margin-top: 23rpx;
    margin-left: 32rpx;
  }
}
.btn-box {
  position: fixed;
  text-align: center;
  bottom: 100rpx;
  right: 60rpx;
  .add-box {
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #2658fa;
    color: #ffffff;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
  .my-box {
    margin-top: 47rpx;
    width: 96rpx;
    height: 96rpx;
    border-radius: 50%;
    background-color: #ffffff;
    color: #000000;
    display: flex;
    justify-content: center;
    flex-direction: column;
  }
}
.black {
  color: #161616;
}
.blod {
  font-weight: bold;
}
.grey {
  color: #808080;
}
</style>
