// uview组件按需引入配置
// 只引入项目中实际使用的组件，减少包大小

import Vue from 'vue'

// 按需引入uview组件
// 根据项目实际使用情况，只引入需要的组件

// 常用组件
import uButton from 'uview-ui/components/u-button/u-button.vue'
import uInput from 'uview-ui/components/u-input/u-input.vue'
import uIcon from 'uview-ui/components/u-icon/u-icon.vue'
import uImage from 'uview-ui/components/u-image/u-image.vue'
import uText from 'uview-ui/components/u-text/u-text.vue'
import uPopup from 'uview-ui/components/u-popup/u-popup.vue'
import uModal from 'uview-ui/components/u-modal/u-modal.vue'
import uLoading from 'uview-ui/components/u-loading/u-loading.vue'
import uToast from 'uview-ui/components/u-toast/u-toast.vue'
import uTabs from 'uview-ui/components/u-tabs/u-tabs.vue'
import uTabsItem from 'uview-ui/components/u-tabs-item/u-tabs-item.vue'
import uSwiper from 'uview-ui/components/u-swiper/u-swiper.vue'
import uCell from 'uview-ui/components/u-cell/u-cell.vue'
import uCellGroup from 'uview-ui/components/u-cell-group/u-cell-group.vue'
import uList from 'uview-ui/components/u-list/u-list.vue'
import uListItem from 'uview-ui/components/u-list-item/u-list-item.vue'
import uForm from 'uview-ui/components/u-form/u-form.vue'
import uFormItem from 'uview-ui/components/u-form-item/u-form-item.vue'
import uTextarea from 'uview-ui/components/u-textarea/u-textarea.vue'
import uPicker from 'uview-ui/components/u-picker/u-picker.vue'
import uDatetimePicker from 'uview-ui/components/u-datetime-picker/u-datetime-picker.vue'
import uActionSheet from 'uview-ui/components/u-action-sheet/u-action-sheet.vue'
import uRate from 'uview-ui/components/u-rate/u-rate.vue'
import uSwitch from 'uview-ui/components/u-switch/u-switch.vue'
import uCheckbox from 'uview-ui/components/u-checkbox/u-checkbox.vue'
import uCheckboxGroup from 'uview-ui/components/u-checkbox-group/u-checkbox-group.vue'
import uRadio from 'uview-ui/components/u-radio/u-radio.vue'
import uRadioGroup from 'uview-ui/components/u-radio-group/u-radio-group.vue'
import uUpload from 'uview-ui/components/u-upload/u-upload.vue'
import uAvatar from 'uview-ui/components/u-avatar/u-avatar.vue'
import uBadge from 'uview-ui/components/u-badge/u-badge.vue'
import uTag from 'uview-ui/components/u-tag/u-tag.vue'
import uProgress from 'uview-ui/components/u-progress/u-progress.vue'
import uCircleProgress from 'uview-ui/components/u-circle-progress/u-circle-progress.vue'
import uLoadMore from 'uview-ui/components/u-loadmore/u-loadmore.vue'
import uEmpty from 'uview-ui/components/u-empty/u-empty.vue'
import uDivider from 'uview-ui/components/u-divider/u-divider.vue'
import uGap from 'uview-ui/components/u-gap/u-gap.vue'
import uSafeBottom from 'uview-ui/components/u-safe-bottom/u-safe-bottom.vue'
import uStatusBar from 'uview-ui/components/u-status-bar/u-status-bar.vue'
import uNavbar from 'uview-ui/components/u-navbar/u-navbar.vue'
import uTabbar from 'uview-ui/components/u-tabbar/u-tabbar.vue'
import uTabbarItem from 'uview-ui/components/u-tabbar-item/u-tabbar-item.vue'
import uBackTop from 'uview-ui/components/u-back-top/u-back-top.vue'
import uNoticeBar from 'uview-ui/components/u-notice-bar/u-notice-bar.vue'
import uCountDown from 'uview-ui/components/u-count-down/u-count-down.vue'
import uCountTo from 'uview-ui/components/u-count-to/u-count-to.vue'
import uSkeleton from 'uview-ui/components/u-skeleton/u-skeleton.vue'
import uSticky from 'uview-ui/components/u-sticky/u-sticky.vue'
import uTransition from 'uview-ui/components/u-transition/u-transition.vue'
import uOverlay from 'uview-ui/components/u-overlay/u-overlay.vue'

// 注册组件
Vue.component('u-button', uButton)
Vue.component('u-input', uInput)
Vue.component('u-icon', uIcon)
Vue.component('u-image', uImage)
Vue.component('u-text', uText)
Vue.component('u-popup', uPopup)
Vue.component('u-modal', uModal)
Vue.component('u-loading', uLoading)
Vue.component('u-toast', uToast)
Vue.component('u-tabs', uTabs)
Vue.component('u-tabs-item', uTabsItem)
Vue.component('u-swiper', uSwiper)
Vue.component('u-cell', uCell)
Vue.component('u-cell-group', uCellGroup)
Vue.component('u-list', uList)
Vue.component('u-list-item', uListItem)
Vue.component('u-form', uForm)
Vue.component('u-form-item', uFormItem)
Vue.component('u-textarea', uTextarea)
Vue.component('u-picker', uPicker)
Vue.component('u-datetime-picker', uDatetimePicker)
Vue.component('u-action-sheet', uActionSheet)
Vue.component('u-rate', uRate)
Vue.component('u-switch', uSwitch)
Vue.component('u-checkbox', uCheckbox)
Vue.component('u-checkbox-group', uCheckboxGroup)
Vue.component('u-radio', uRadio)
Vue.component('u-radio-group', uRadioGroup)
Vue.component('u-upload', uUpload)
Vue.component('u-avatar', uAvatar)
Vue.component('u-badge', uBadge)
Vue.component('u-tag', uTag)
Vue.component('u-progress', uProgress)
Vue.component('u-circle-progress', uCircleProgress)
Vue.component('u-loadmore', uLoadMore)
Vue.component('u-empty', uEmpty)
Vue.component('u-divider', uDivider)
Vue.component('u-gap', uGap)
Vue.component('u-safe-bottom', uSafeBottom)
Vue.component('u-status-bar', uStatusBar)
Vue.component('u-navbar', uNavbar)
Vue.component('u-tabbar', uTabbar)
Vue.component('u-tabbar-item', uTabbarItem)
Vue.component('u-back-top', uBackTop)
Vue.component('u-notice-bar', uNoticeBar)
Vue.component('u-count-down', uCountDown)
Vue.component('u-count-to', uCountTo)
Vue.component('u-skeleton', uSkeleton)
Vue.component('u-sticky', uSticky)
Vue.component('u-transition', uTransition)
Vue.component('u-overlay', uOverlay)

// 引入uview的JS工具函数
import { $u } from 'uview-ui'
Vue.prototype.$u = $u

export default {
  install(Vue) {
    // 这里可以添加全局配置
  }
}
