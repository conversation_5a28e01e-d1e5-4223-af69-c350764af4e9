{
  "name": "supply<PERSON><PERSON><PERSON><PERSON><PERSON>",
  "appid": "__UNI__5D214B6",
  "description": "",
  "versionName": "1.0.4",
  "versionCode": "100",
  "transformPx": false,
  "app-plus": {
    /* 5+App特有相关 */
    "usingComponents": true,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {
      "VideoPlayer": {}
    },
    /* 模块配置 */
    "distribute": {
      /* 应用发布信息 */
      "android": {
        /* android打包配置 */
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
          "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
          "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CAMERA\"/>",
          "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
          "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
          "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
          "<uses-feature android:name=\"android.hardware.camera\"/>",
          "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
        ]
      },
      "ios": {},
      /* ios打包配置 */
      "sdkConfigs": {
        "ad": {}
      }
    }
  },
  /* SDK配置 */
  "quickapp": {},
  /* 快应用特有相关 */
  "mp-weixin": {
    /* 微信小程序特有相关 */
    "appid": "wxbe88683bd339aaf5",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "minified": true,
      "postcss": true,
      "minifyWXSS": true,
      "minifyWXML": true
    },
    "usingComponents": true,
    "optimization": {
      "subPackages": true,
      "treeShaking": {
        "enable": true
      }
    },
    "__usePrivacyCheck__": true,
    // 懒加载优化
    "lazyCodeLoading": "requiredComponents",
    // 启用代码压缩
    "minifyJS": true,
    "minifyWXML": true,
    "minifyWXSS": true,
    // 启用代码保护
    "codeProtect": false,
    // 启用插件功能
    "plugins": {},
    // 云开发配置
    "cloudfunctionRoot": "",
    // 分包预下载
    "preloadRule": {
      "pages/index/index": {
        "network": "all",
        "packages": ["packageA"]
      },
      "pages/classify/classify": {
        "network": "all",
        "packages": ["packageA"]
      }
    }
  },
  "mp-alipay": {
    "usingComponents": true
  },
  "mp-baidu": {
    "usingComponents": true
  },
  "mp-toutiao": {
    "usingComponents": true
  },
  "mp-qq": {
    "usingComponents": true
  },
  "h5": {
    "title": "供应链",
    "domain": "",
    "router": {
      "base": "/h5/",
      "mode": "hash"
    },
    "devServer": {
      "port": 8080,
      "disableHostCheck": true,
      "proxy": {
        "/api": {
          "target": "https://supply.yunzmall.com/",
          "changeOrigin": true,
          "secure": true,
          "pathRewrite": {
            "^/api": ""
          }
        }
      }
    },
    "sdkConfigs": {
      "maps": {}
    },
    "template": "template.h5.html",
    "optimization": {
      "treeShaking": {
        "enable": true
      }
    }
  }
}
