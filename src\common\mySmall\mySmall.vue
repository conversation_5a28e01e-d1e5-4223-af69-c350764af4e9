<template>
  <view>
    <view class="my_order mb-20 b-r-10">
      <view class="order_title font_size14 f-bold mb-20">
        {{ mYtitle }}
      </view>
      <u-line></u-line>
      <view class="order_content">
        <u-grid :border="false" col="3">
          <u-grid-item class="mt_20">
            <view class="small-money mt_20">
              ￥{{ this.toYuan(smallData.order_price_sum) }}
            </view>
            <view class="mt_16">总订单（元）</view>
          </u-grid-item>
          <u-grid-item class="mt_20" @click="toOrder">
            <view class="small-money mt_20">
              {{ smallData.order_count }}
            </view>
            <view class="mt_16">总订单（单）</view>
          </u-grid-item>
          <u-grid-item class="mt_20" @click="toSettle">
            <view class="small-money mt_20">
              ￥{{ this.toYuan(smallData.all_settle_sum) }}
            </view>
            <view class="mt_16">总收益（元）</view>
          </u-grid-item>
        </u-grid>
        <u-grid :border="false" col="4">
          <u-grid-item
            v-for="(listItem, listIndex) in mylist"
            :key="listIndex"
            @click="onClickNav(listItem.url)"
          >
            <view
              class="iconfont"
              :class="listItem.nameClass"
              v-if="listItem.nameClass"
            ></view>
            <image
              :src="listItem.nameImg"
              v-if="listItem.nameImg"
              mode="widthFix"
              class="iconf-img"
            />
            <text class="grid-text font_size12">
              {{ listItem.title }}
            </text>
          </u-grid-item>
          <u-grid-item v-if="checkWenxin()">
            <wx-open-launch-weapp
              id="launch-btn"
              :appid="appid"
              :path="'pages/index/index.html?sid=' + sid"
            >
              <script type="text/wxtag-template">
                <view style="display: block;width: 40px;height: 40px;margin-left: 4px;margin-top: 12.5px;background: rgba(255,255,255);border-radius: 50%;">
                  <image style="display: block;width: 30px;height: 30px;margin: 0 auto;padding-top: 6px;" src="https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/2024517/1715917398组 1057.png"></image>
                </view>
                <view style="display: block;margin-top: 2px;width: 100%; font-size: 12px">店铺首页</view>
              </script>
            </wx-open-launch-weapp>
          </u-grid-item>
          <!-- #ifdef MP-WEIXIN -->
          <u-grid-item @click="toJump">
            <view class="iconf-img iconfont icon-shouye"></view>
            <view class="grid-text font_size12">店铺首页</view>
          </u-grid-item>
          <!-- #endif -->
        </u-grid>
      </view>
    </view>
    <!-- <myTabBar ref="myTabBar"></myTabBar> -->
  </view>
</template>
<script>
import myTabBar from '../../components/tabbar.vue'

export default {
  name: 'mySmall',
  components: {
    myTabBar,
  },
  props: {
    mYtitle: String,
    mylist: Array,
    isShowShop: Number,
  },
  data() {
    return {
      smallData: {}, // 订单信息
      sid: '', // 小商店店主的id
      appid: '', // 小程序的appid
      popShow: false,
      value: '',
      modalShow: false,
      title: '提示',
      content: '是否重新提交小程序申请？',
    }
  },
  created() {
    this.getSmallShopStatistics()
    this.onLoadSetting()
    // #ifdef MP-WEIXIN
    this.isMPWEIXIN = true
    // #endif
    if (this.checkWenxin() || this.isMPWEIXIN) {
      this.get('/api/smallShop/setting/getMiniAppID').then(res => {
        this.appid = res.data.appid
      })
    }
    // 微信浏览器
    if (this.checkWenxin()) {
      const url = window.location.href.split('#')[0]
      this.get('/api/wechatofficial/getJsConfig', {
        url,
      }).then(res => {
        if (res.code === 0) {
          const data = res.data
          jweixin.config({
            debug: false,
            appId: data.app_id,
            timestamp: data.timestamp,
            nonceStr: data.nonce_str,
            signature: data.signature,
            jsApiList: ['chooseImage'],
            openTagList: ['wx-open-launch-weapp'],
          })
        }
      })
    }
  },
  methods: {
    // 获取店铺收益
    async getSmallShopStatistics() {
      const res = await this.get(
        '/api/wechatmini/getSmallShopStatistics',
        {},
        true,
      )
      if (res.code === 0) {
        this.smallData = res.data.smallShopStatistics
      }
    },
    // 跳转页面
    onClickNav(url) {
      this.navTo(url)
    },
    // 跳转订单
    toOrder() {
      this.navTo('/packageD/smallShop/smallShopOrder')
    },
    // 跳转推广
    toSettle() {
      if (uni.getStorageSync('showPromotion')) {
        setTimeout(() => {
          const data = uni.getStorageSync('tabPromotion')
          this.$refs.myTabBar.handleChange(data)
        }, 100)
        this.navTo('/pages/promotion/promotion')
      } else {
        this.toast('请选择推广导航')
      }
    },
    // 获取店铺设置
    async onLoadSetting() {
      const res = await this.get(
        '/api/smallShop/setting/getShopSetting',
        {},
        true,
      )
      this.sid = res.data.setting.sid // 小商店店主的id
    },
    // 跳转至小商店用户端
    toJump() {
      if (!this.sid) {
        this.toast('请先配置店铺')
        return
      }
      // #ifdef MP-WEIXIN
      uni.navigateToMiniProgram({
        appId: this.appid.trim(), // 要打开的小程序 appId
        path: 'pages/index/index?sid=' + this.sid, // 首页
        success(res) {},
        fail(res) {},
        complete(res) {},
      })
      // #endif
    },
  },
}
</script>
<style lang="scss" scoped>
.small-money {
  color: #f15353;
  font-size: 28rpx;
  font-weight: 600;
}
.my_order {
  background-color: #fff;
  padding: 25rpx 30rpx 30rpx 30rpx;
}
.order_content {
  .iconfont {
    font-size: 48rpx;
    margin: 42rpx 0 14rpx 0;
  }
  //设置图标的样式
  .icon_blue {
    font-size: 40rpx;
    color: #21a9f5;
  }
  .icon_orange {
    font-size: 40rpx;
    color: #f3702d;
  }
  .icon_violet {
    font-size: 40rpx;
    color: #a558ed;
  }
  .iconf-img {
    margin: 34rpx 0 10rpx 0;
    width: 56rpx;
    height: 56rpx;
    display: block;
  }
  .icon_small_shop {
    margin-bottom: 3rpx;
  }
  .icon_yellow {
    font-size: 40rpx;
    color: #f3a51d;
  }
}
</style>
