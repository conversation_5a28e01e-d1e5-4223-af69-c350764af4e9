// 懒加载工具函数
// 用于动态导入组件和模块，减少初始包大小

/**
 * 懒加载组件
 * @param {Function} importFunc 动态导入函数
 * @param {Object} options 配置选项
 * @returns {Object} Vue异步组件配置
 */
export function lazyLoadComponent(importFunc, options = {}) {
  const defaultOptions = {
    loading: {
      template: '<view class="loading">加载中...</view>'
    },
    error: {
      template: '<view class="error">加载失败</view>'
    },
    delay: 200,
    timeout: 10000
  }
  
  const config = { ...defaultOptions, ...options }
  
  return () => ({
    component: importFunc(),
    loading: config.loading,
    error: config.error,
    delay: config.delay,
    timeout: config.timeout
  })
}

/**
 * 懒加载页面
 * @param {String} path 页面路径
 * @returns {Function} 动态导入函数
 */
export function lazyLoadPage(path) {
  return () => import(`@/${path}`)
}

/**
 * 懒加载第三方库
 * @param {String} libName 库名称
 * @returns {Function} 动态导入函数
 */
export function lazyLoadLibrary(libName) {
  return () => import(libName)
}

/**
 * 条件懒加载
 * @param {Function} condition 条件函数
 * @param {Function} importFunc 导入函数
 * @returns {Promise} 
 */
export async function conditionalLazyLoad(condition, importFunc) {
  if (condition()) {
    return await importFunc()
  }
  return null
}

/**
 * 预加载组件
 * @param {Array} components 组件列表
 */
export function preloadComponents(components) {
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      components.forEach(component => {
        if (typeof component === 'function') {
          component()
        }
      })
    })
  } else {
    // 降级处理
    setTimeout(() => {
      components.forEach(component => {
        if (typeof component === 'function') {
          component()
        }
      })
    }, 2000)
  }
}

/**
 * 路由懒加载
 * @param {String} path 路径
 * @returns {Function} 路由组件
 */
export function lazyRoute(path) {
  return lazyLoadComponent(() => import(`@/${path}`))
}

/**
 * 按需加载工具函数
 */
export const LazyLoadUtils = {
  // 懒加载uview组件
  loadUviewComponent(componentName) {
    return () => import(`uview-ui/components/u-${componentName}/u-${componentName}.vue`)
  },
  
  // 懒加载腾讯云IM
  loadTencentIM() {
    return () => import('@tencentcloud/chat')
  },
  
  // 懒加载播放器
  loadXGPlayer() {
    return Promise.all([
      import('xgplayer'),
      import('xgplayer-hls')
    ])
  },
  
  // 懒加载图表库
  loadCharts() {
    return () => import('echarts')
  },
  
  // 懒加载二维码库
  loadQRCode() {
    return () => import('qrcode')
  }
}

/**
 * 组件注册器 - 按需注册组件
 */
export class ComponentRegistry {
  constructor(Vue) {
    this.Vue = Vue
    this.registeredComponents = new Set()
  }
  
  // 注册单个组件
  registerComponent(name, component) {
    if (!this.registeredComponents.has(name)) {
      this.Vue.component(name, component)
      this.registeredComponents.add(name)
    }
  }
  
  // 批量注册组件
  registerComponents(components) {
    Object.keys(components).forEach(name => {
      this.registerComponent(name, components[name])
    })
  }
  
  // 懒注册组件
  lazyRegisterComponent(name, importFunc) {
    if (!this.registeredComponents.has(name)) {
      this.Vue.component(name, lazyLoadComponent(importFunc))
      this.registeredComponents.add(name)
    }
  }
}

export default {
  lazyLoadComponent,
  lazyLoadPage,
  lazyLoadLibrary,
  conditionalLazyLoad,
  preloadComponents,
  lazyRoute,
  LazyLoadUtils,
  ComponentRegistry
}
