// vue.config.js，如没有此文件则手动创建
const path = require('path')

module.exports = {
  transpileDependencies: ['uview-ui'],

  // 生产环境优化配置
  configureWebpack: config => {
    if (process.env.NODE_ENV === 'production') {
      // 代码分割优化
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            // 将第三方库单独打包
            vendor: {
              name: 'vendor',
              test: /[\\/]node_modules[\\/]/,
              chunks: 'all',
              priority: 10,
              enforce: true,
            },
            // uview-ui单独打包
            uview: {
              name: 'uview',
              test: /[\\/]node_modules[\\/]uview-ui[\\/]/,
              chunks: 'all',
              priority: 20,
              enforce: true,
            },
            // 腾讯云IM单独打包
            tencentcloud: {
              name: 'tencentcloud',
              test: /[\\/]node_modules[\\/]@tencentcloud[\\/]/,
              chunks: 'all',
              priority: 20,
              enforce: true,
            },
            // xgplayer单独打包
            xgplayer: {
              name: 'xgplayer',
              test: /[\\/]node_modules[\\/]xgplayer[\\/]/,
              chunks: 'all',
              priority: 20,
              enforce: true,
            },
          },
        },
      }
    }
  },

  // CSS优化
  css: {
    extract:
      process.env.NODE_ENV === 'production'
        ? {
            ignoreOrder: true,
          }
        : false,
    loaderOptions: {
      scss: {
        // 全局scss变量
        additionalData: `@import "@/uni.scss";`,
      },
    },
  },

  // 链式操作配置
  chainWebpack: config => {
    // 生产环境优化
    if (process.env.NODE_ENV === 'production') {
      // 移除console
      config.optimization.minimizer('terser').tap(args => {
        args[0].terserOptions.compress.drop_console = true
        args[0].terserOptions.compress.drop_debugger = true
        return args
      })
    }

    // 设置别名
    config.resolve.alias
      .set('@', path.resolve(__dirname, 'src'))
      .set('common', path.resolve(__dirname, 'src/common'))
      .set('components', path.resolve(__dirname, 'src/components'))
      .set('static', path.resolve(__dirname, 'src/static'))
      .set('utils', path.resolve(__dirname, 'src/utils'))
  },
}
