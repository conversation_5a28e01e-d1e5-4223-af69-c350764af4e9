<!-- 登录 -->
<template>
  <!-- #ifndef MP  -->
  <view class="login_box">
    <view class="header-box">
      <view class="welcome-box">
        <view class="h1-view">欢迎使用</view>
        <view class="h1-view">{{ welcome }}</view>
      </view>
    </view>
    <view class="main-view">
      <view class="pl_50 pr_50 pt_10">
        <u-tabs
          @change="handleLoginType"
          :list="tabsList"
          :current="loginType"
          lineColor="#F15353"
          :activeStyle="{ fontWeight: '700' }"
          :inactiveStyle="{
            width: '100%',
            height: '100%',
            textAlign: 'center',
            lineHeight: '44px',
            background: 'linear-gradient(180deg, #F5F5F5 0%, #FFFFFF 100%)',
            borderRadius: '50px',
          }"
          lineWidth="32rpx"
          :lineHeight="8"
        ></u-tabs>
        <!-- 注意，如果需要兼容微信小程序，最好通过setRules方法设置rules规则 -->
        <u--form
          labelPosition="left"
          :model="loginForm"
          :rules="rules"
          ref="loginForm"
          class="mt_30"
        >
          <u-form-item prop="userName">
            <u--input
              v-model="loginForm.userName"
              placeholder="请输入您的手机号"
              border="surround"
              shape="circle"
              ref="phone"
              clearable
              :customStyle="inputStyle"
            ></u--input>
          </u-form-item>
          <u-form-item prop="captcha_code" v-if="loginType === 0">
            <u-input
              v-model="loginForm.captcha_code"
              placeholder="请输入验证码"
              border="surround"
              shape="circle"
              :customStyle="inputStyle"
              @input="handleCode"
              clearable
            >
              <template slot="suffix">
                <button
                  class="Usercode"
                  :class="isDisable ? 'sendCode' : ''"
                  size="mini"
                  @click="$clicks(sendCode)"
                  :disabled="isDisable"
                >
                  {{ codeText }}
                </button>
              </template>
            </u-input>
          </u-form-item>
          <u-form-item prop="password" v-if="loginType === 1">
            <u--input
              type="password"
              v-model="loginForm.password"
              placeholder="请输入密码"
              border="surround"
              shape="circle"
              :customStyle="inputStyle"
              clearable
            ></u--input>
          </u-form-item>
          <u-form-item>
            <u-button
              text="登录"
              @click="login"
              type="error"
              shape="circle"
            ></u-button>
          </u-form-item>
        </u--form>
        <view class="user_btn">
          <view class="d-cc" @click="navTo('/pages/register/register')">
            没有账号？
            <text class="c-f15">点击注册</text>
          </view>
        </view>
      </view>
    </view>
    <view class="f fjc">
      <agreement
        class="agreement"
        @confirmed="confirmed"
        :agreemenTtype="type"
      ></agreement>
    </view>
    <u-modal
      :show="isShow"
      title="账号注册审核提示"
      confirmText="回到首页"
      @confirm="backIndex"
    >
      <view class="slot-content">
        <rich-text :nodes="modalContent"></rich-text>
      </view>
    </u-modal>
  </view>
  <!-- #endif -->
  <!-- #ifdef MP-WEIXIN-->
  <view class="appletsLogin">
    <view class="ggbg">
      <div class="box">
        <image
          :src="
            avatar
              ? avatar
              : 'https://supply-chain-yunzmall.oss-cn-beijing.aliyuncs.com/202275/1657009728lBQswzntYgwO88069ba0c2df26409482fe0d55a7f7b7.png'
          "
        ></image>
        <div class="mt-15" v-if="!avatar">微信用户</div>
      </div>
    </view>
    <view class="content">
      <view class="d-cc-c fs-3" style="margin-top: 190rpx">
        <view class="btn1" @click="miniLogin">授权登录</view>
        <view class="mt-55 btn2" @click="back">暂不登录</view>
      </view>
    </view>
    <view class="foot">
      <view class="fs-3">申请获取以下权限</view>
      <view class="mt-30 c-666666">获取你的公开信息（昵称，头像等）</view>
    </view>
  </view>
  <!-- #endif -->
</template>

<script>
import agreement from '@/common/agreement/agreement.vue'
export default {
  components: {
    agreement,
  },
  data() {
    return {
      isShow: false,
      modalContent: '',
      pass: false,
      inputStyle: { backgroundColor: '#F9F9F9' },
      loginType: 0, // 0 = 验证码登录 1 = 密码登录
      tabsList: [
        {
          name: '验证码登录',
        },
        {
          name: '密码登录',
        },
      ],
      avatar: '',
      loginForm: {
        userName: '',
        captcha_key: '',
        captcha_code: '',
        password: '',
      },
      userShow: false,
      type: 'login',
      isDisable: false,
      codeText: '获取短信验证码',
      logoSrc: '',
      welcome: '',
      rules: {
        userName: [
          {
            type: 'string',
            required: true,
            message: '请填写手机号',
            trigger: ['blur'],
          },
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              // 上面有说，返回true表示校验通过，返回false表示不通过
              // uni.$u.test.mobile()就是返回true或者false的
              this.userShow = uni.$u.test.mobile(value)
              return uni.$u.test.mobile(value)
            },
            message: '手机号码不正确',
            // 触发器可以同时用blur和change
            trigger: ['blur'],
          },
        ],
        captcha_code: {
          type: 'number',
          max: 10,
          validator: (rule, value, callback) => {
            if (this.loginType === 0 && !value) {
              callback(new Error('请输入验证码'))
            } else {
              callback()
            }
          },
        },
        password: {
          type: 'string',
          validator: (rule, value, callback) => {
            if (this.loginType === 1 && !value) {
              callback(new Error('请输入密码'))
            } else {
              callback()
            }
          },
        },
      },
    }
  },
  onReady() {
    //#ifndef MP
    this.$refs.loginForm.setRules(this.rules)
    //#endif
  },
  onShow() {
    //#ifndef MP
    this.framework()
    //#endif
  },
  onLoad() {
    uni.removeStorageSync('token')
    uni.removeStorageSync('user')
  },
  methods: {
    // 回到首页
    backIndex() {
      this.navsTo('/pages/index/index')
    },
    // 处理验证码
    handleCode() {
      if (this.loginForm.captcha_code.length > 4) {
        this.loginForm.captcha_code.slice(0, 4)
      }
    },
    confirmed(status) {
      this.pass = status
    },
    // 切换登录方式
    handleLoginType(v) {
      /* switch(this.loginType){
					case 1:
						this.loginType = 2
						break;
					case 2:
						this.loginType = 1
						break;
				}
				this.loginForm.captcha_code = ""
				this.loginForm.password = "" */
      this.loginType = v.index
      this.loginForm.captcha_code = ''
      this.loginForm.password = ''
    },
    sendCode() {
      if (!this.pass) {
        this.toast('请先阅读《用户协议》，并勾选我已阅读并同意后继续！')
        return
      }
      if (this.userShow) {
        this.post('/api/user/sendCode', {
          mobile: this.loginForm.userName,
          type: this.type,
        })
          .then(res => {
            if (res.code === 0) {
              let data = res.data
              this.loginForm.captcha_key = data.captcha_key
              this.timeReduce()
            } else {
              this.toast(res.msg)
            }
          })
          .catch(Error => {})
      } else {
        this.toast('请输入手机号')
      }
    },
    login() {
      if (!this.pass) {
        this.toast('请先阅读《用户协议》，并勾选我已阅读并同意后继续！')
        return
      }

      this.$refs.loginForm
        .validate()
        .then(res => {
          let params = {
            userName: this.loginForm.userName,
          }
          // 当H5本地存储有pid时登录时携带上
          // #ifdef H5
          if (localStorage.getItem('pid')) {
            params.pid = parseInt(localStorage.getItem('pid'))
          }
          // #endif
          let url = ''
          switch (this.loginType) {
            case 0:
              url = '/api/user/loginWithCode'
              params.captcha_code = this.loginForm.captcha_code
              params.captcha_key = this.loginForm.captcha_key
              break
            case 1:
              url = '/api/user/login'
              params.password = this.loginForm.password
              break
          }
          this.post(url, params)
            .then(res => {
              if (res.code === 0) {
                let data = res.data
                // 审核中
                if (data.status === 0) {
                  this.isShow = true
                  this.get('/api/user/findSettingAccord').then(settingRes => {
                    this.modalContent = settingRes.data.audit_agreement
                  })
                  return
                }
                uni.setStorageSync('token', data.token)
                uni.setStorageSync('user', data.user)
                uni.setStorageSync('type', 'refresh')
                this.toast(res.msg)
                const pages = getCurrentPages()
                let index = null
                for (let i = 0; i < pages.length; i++) {
                  if (pages[i].route === 'pages/login/login') {
                    index = i - 1
                    break
                  }
                }
                const parentLink = pages[index]?.$page?.fullPath
                if (parentLink) {
                  if (
                    parentLink === 'pages/index/index' ||
                    parentLink === '/pages/index/index'
                  ) {
                    uni.setStorageSync('type', 'refresh')
                    setTimeout(() => {
                      uni.redirectTo({
                        url: '/pages/index/index',
                      })
                    }, 1500)
                    return
                  } else {
                    let firstChar = parentLink.charAt(0)
                    let jumpUrl =
                      firstChar === '/' ? parentLink : '/' + parentLink
                    setTimeout(() => {
                      uni.redirectTo({
                        url: jumpUrl,
                      })
                    }, 1500)
                  }
                } else {
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/pages/index/index',
                    })
                  }, 1500)
                }

                // this.form.captcha_key = data.captcha_key
              } else {
                this.toast(res.msg)
              }
            })
            .catch(Error => {})
        })
        .catch(errors => {
          uni.$u.toast('填写错误')
        })
    },
    timeReduce() {
      // 定时器方法
      let _this = this
      let time = 60
      this.timer = setInterval(() => {
        this.codeText = `重新发送${time}s`
        time -= 1
        this.isDisable = true
        if (time <= 0) {
          this.isDisable = false
          this.codeText = '获取短信验证码'
          clearInterval(_this.timer)
        }
      }, 500)
    },
    back() {
      // this.navsTo('/pages/membercenter/membercenter')
      //  this.navBack()
      const pages = getCurrentPages()
      let index = null
      for (let i = 0; i < pages.length; i++) {
        if (pages[i].route === 'pages/login/login') {
          index = i - 1
          break
        }
      }
      const parentLink = pages[index]?.$page?.fullPath
      if (parentLink) {
        setTimeout(() => {
          uni.redirectTo({
            url: parentLink,
          })
        }, 1500)
      } else {
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/index/index',
          })
        }, 1500)
      }
    },
    framework() {
      this.get('/api/home/<USER>', {}, true)
        .then(res => {
          if (res.code === 0) {
            let data = res.data
            this.logoSrc = data.header?.logo_src
            this.welcome = data.header?.welcome
          } else {
            this.toast(res.msg)
          }
        })
        .catch(Error => {})
    },
  },
}
</script>
<style lang="scss" scoped>
/* #ifdef MP-WEIXIN */
::v-deep .loginCode .u-form-item__body__right__content__slot {
  flex-direction: row;
}

/* #endif */
</style>
<style lang="scss" scoped>
page {
  background-color: #fff;
}
.agreement {
  position: fixed;
  bottom: 50rpx;
}
.appletsLogin {
  .ggbg {
    /* 背景*/
    background: linear-gradient(to right, #fe7f56, #fd3d57);
    width: 100%;
    height: 300rpx;

    .box {
      z-index: 99;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
      top: 110rpx;
      left: 310rpx;
    }

    image {
      border-radius: 50%;
      width: 150rpx;
      height: 150rpx;
    }
  }

  .content {
    position: absolute;
    top: 180rpx;
    left: 25rpx;
    height: 600rpx;
    width: 93.5%;
    border-radius: 7rpx;
    background-color: white;
    box-shadow: 0.1rpx 9rpx 10rpx 0rpx rgba(213, 213, 213, 0.59);

    .btn1 {
      background-color: #f14e4e;
      color: white;
      border-radius: 50rpx;
      padding: 30rpx;
      width: 80%;
      text-align: center;
    }

    .btn2 {
      background-color: white;
      color: #414141;
      border-radius: 50rpx;
      padding: 30rpx;
      width: 80%;
      text-align: center;
      border: #e6e6e6 1rpx solid;
    }
  }

  .foot {
    position: absolute;
    top: 815rpx;
    left: 25rpx;
  }
}

.login_box {
  .login_img {
    margin: 55rpx 0 108rpx 0;
  }

  .login_form {
    padding: 0 30rpx;

    .eyes {
      display: inline-block;
      margin-top: 8rpx;
      color: #999;
      font-size: 60rpx;
    }

    .Usercode {
      width: 220rpx;
      height: 60rpx;
      line-height: 60rpx;
      font-size: 24rpx;
      padding: 0;
      text-align: center;
      background-color: #ededed;
      color: #f15353;
      border-radius: 30rpx;
    }
  }

  .user_btn {
    margin-top: 20rpx;
    padding: 0 75rpx;

    .login {
      width: 100%;
      height: 88rpx;
      background-color: #f15353;
      border-radius: 44px;
      color: #fff;
    }

    .register {
      width: 100%;
      height: 88rpx;
      border-radius: 44px;
      border: solid 2px #f15353;
      color: #f15353;
      margin-top: 42rpx;
    }
  }
}

.login_box {
  height: 100vh;
  background-color: #fff;
  .header-box {
    position: relative;
    width: 100%;
    height: 439rpx;
    background-image: url('https://yunxingongyinglian.oss-cn-guangzhou.aliyuncs.com/2023124/1701654061logo_img.png');
    background-repeat: no-repeat;
    background-size: 100%;
    .welcome-box {
      position: absolute;
      bottom: 110rpx;
      padding: 0 50rpx;
      .h1-view {
        font-size: 50rpx;
        font-weight: bold;
      }
    }
  }
  .main-view {
    position: absolute;
    top: 358rpx;
    left: 0;
    right: 0;
    background: #fff;
    border-top-left-radius: 50rpx;
    border-top-right-radius: 50rpx;
    .user_btn {
      .c-f15 {
        color: #f15353;
      }
    }
  }
  .smallUsercode {
    width: 210rpx;
    font-size: 22rpx;
    color: #f15353;
  }
}
::v-deep .u-tabs__wrapper__nav__item {
  flex: 1;
}
::v-deep .Usercode {
  color: #f15353;
  &.sendCode {
    color: #aaaab3;
  }
}
::v-deep .Usercode:after {
  border: none;
  border-left: 3px solid #ececec;
  border-radius: 0;
}
</style>
