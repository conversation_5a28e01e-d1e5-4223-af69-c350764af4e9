<template>
  <view>
    <block v-for="(item, index) in list" :key="index">
      <view class="goodsItem mt-40" @click="goProductDetail(item.id)">
        <u--image
          :showLoading="true"
          width="170rpx"
          height="170rpx"
          :src="item.thumb"
        ></u--image>
        <view class="rightItem ml-20">
          <view>
            <text class="u-line-2">{{ item.title }}</text>
          </view>
          <view class="f fac fjsb mr-10">
            <text class="font_size12 c-gray">已售:{{ item.sales }}</text>
            <view>
              <text class="font_size12 c-orange">
                建议零售价:{{
                  '¥' + toYuan(item.origin_price)
                }}
              </text>
          </view>
          </view>
          <view>
            <text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="font_size12 c-orange">
              利润:{{
                '¥' + toYuan(item.level_profit)
              }}
            </text>
            <text v-else-if="userLevelPriceTitle" class="font_size12 c-orange">
              利润:{{
                '¥' + userLevelPriceTitle
              }}
            </text>
            <text v-else class="font_size12 c-orange" @click.stop="jumpLogin">
              利润:{{
                checkNull(user)
                  ? '¥' + toYuan(item.level_profit)
                  : '价格登录可见'
              }}
            </text>
          </view>
          <view class="d-bf c-orange">
            <text v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)" class="font_size16"
              ><template v-if="is_web_super_wholesal_price === 0"><text class="font_size12">¥</text
              >{{ toYuan(item.price) }}</template></text
            >
            <text v-else-if="userLevelPriceTitle" class="font_size16"
              ><template v-if="is_web_super_wholesal_price === 0"><text class="font_size12">¥</text
              >{{ userLevelPriceTitle }}</template></text
            >
            <!-- {{ checkNull(user) ? getSuperPrice(item) : item.origin_price }} -->
            <text v-else class="font_size16"
              ><template v-if="is_web_super_wholesal_price === 0"><text class="font_size12">¥</text
              >{{ checkNull(user) ? toYuan(item.price) : '价格登录可见' }}</template></text
            >
            <view
              class="shoppingCartView d-cc mr_10"
              @click.stop="openSkus(item)"
              v-if="userLevelPriceTitle && isMatchingPriceAuthority(item.id)"
            >
              <u-icon name="shopping-cart" color="#fff"></u-icon>
              <view class="badgeView d-cc font_size12 c-37" v-if="item.qty">
                {{ item.qty }}
              </view>
            </view>
            <view
              class="shoppingCartView d-cc mr_10"
              @click.stop="openSkus(item)"
              v-else-if="checkNull(user) && !userLevelPriceTitle"
            >
              <u-icon name="shopping-cart" color="#fff"></u-icon>
              <view class="badgeView d-cc font_size12 c-37" v-if="item.qty">
                {{ item.qty }}
              </view>
            </view>
          </view>
        </view>
      </view>
    </block>
    <view class="d-cc fs-1 c-5e5e5e mb-25 mt-25">
      <view v-if="readMore && list.length != 0">暂无更多~</view>
      <view v-if="list.length == 0">暂无数据~</view>
    </view>
  </view>
</template>
<script>
export default {
  props: {
    // 商品数据
    list: {
      type: Array,
    },
    user: {
      default: () => {
        return uni.getStorageSync('user')
      },
    },
  },
  computed: {
    userLevelPriceTitle(){
      return this.$store.state.userLevelPriceTitle || ""
    },
    totalPrice() {
      const price = this.toYuan(
        this.accMul(
          this.popupData.checkSkus.price,
          this.popupData.specificationsNum,
        ),
      )
      return price
    },
    readMore() {
      // 为了适配小程序端{{}}不能直接读取vuex值的问题
      return this.$store.state.homeNoMore
    },
  },
  data() {
    return {
      // skus弹框数据
      popupData: {
        isShow: false,
        data: {},
        specificationsSet: [], // skus
        checkSkus: {}, // 选中的规格
        specificationsNum: 1, // 规格选择数量,
        productID: null,
      },
      is_web_super_wholesal_price:0
    };
  },
  created(){
    this.get('/api/home/<USER>', {}, true).then(data => {
				this.is_web_super_wholesal_price = data.data.page_setup_setting.pc_list_setting.is_web_super_wholesal_price
			})
  },
  methods: {
    handleClose() {
      this.popupData = {
        isShow: false,
        data: {},
        specificationsSet: [], // skus
        checkSkus: {}, // 选中的规格
        specificationsNum: 1, // 规格选择数量,
        productID: null,
      }
    },
    // 商品详情跳转
    goProductDetail(id) {
      this.navTo(
        '/packageA/commodity/commodity_details/commodity_details?id=' + id,
      )
    },
    // 切换规格
    specificationIsSelected(index) {
      for (let i = 0; i < this.popupData.specificationsSet.length; i++) {
        // 添加规格是否选中是否镂空显示
        if (index != i) {
          this.$set(this.popupData.specificationsSet[i], 'check', true)
        }
        this.$set(this.popupData.specificationsSet[index], 'check', false)
        this.popupData.checkSkus = this.popupData.specificationsSet[index]
      }
    },
    // 选规格
    async openSkus(item) {
      this.$emit('openSkus',item)
    },
    // 获取商品详情
    async getProductData(id) {
      this.popupData.productID = id
      const res = await this.get('/api/product/get', { id }, true)
      return res
    },
    // 加入购物车
    async addShoppingCart() {
      if (this.specificationsNum >= this.popupData.checkSkus.stock) {
        this.showText('选择的数量大于库存啦')
        return
      }
      const params = {
        shopping_carts: [
          {
            qty: this.popupData.specificationsNum,
            sku_id: this.popupData.checkSkus.id,
          },
        ],
      }
      const res = await this.post('/api/shoppingcart/add', params, true)
      if (res.code === 0) {
        uni.showToast({
          title: res.msg,
          icon: 'none',
          duration: 2000,
        })
        this.$emit('updateQTY', {
          qty: this.popupData.specificationsNum,
          productID: this.popupData.productID,
        })
      }
    },
    jumpLogin() {
      if (!this.user) {
        this.navTo('/pages/login/login')
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.goodsItem {
  height: 170rpx;
  display: flex;
  .rightItem {
    flex: 1;
    height: 170rpx;
    display: flex;
    flex-flow: column;
    justify-content: space-between;
    .shoppingCartView {
      position: relative;
      width: 50rpx;
      height: 50rpx;
      border-radius: 50%;
      background-color: #f15353;
      .badgeView {
        width: 35rpx;
        height: 35rpx;
        background-color: #ffcc33;
        position: absolute;
        top: -15rpx;
        right: -10rpx;
        border-radius: 50%;
      }
    }
  }
}
.u-popup-slot {
  width: 80vw;
  padding: 50rpx 30rpx;
  position: relative;
  .myCloseView {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -90rpx;
  }
}
</style>
