<!-- 失效商品的组件 -->
<template>
	<view>
		<block v-for="(item,index) in failureList" :key="item.goods_id">
			<view class="failure b-r-10 mb-20">
				<view class="failure_title">
					<u-row customStyle="margin-bottom: 10px;font-size:24rpx;"  justify="space-between">
							<u-col span="6">
									<view class="demo-layout bg-purple">失效商品{{item.failureNum}}件</view>
							</u-col>
							<u-col span="6">
									<view class="demo-layout c-f1 d-ef">清空失效商品</view>
							</u-col>
					</u-row>
				</view>
				<view class="shopping_box d-c">
					<view class="shop_box_left d-cc">
						<view class="failure_btn font_size12 f-ping-fang text-c">失效</view>
						<image :src="item.img"></image>
					</view>
					<view class="shop_box_right d-be">
						
						<u--text size="14" 
											color="#333" 
											lineHeight="36rpx" 
											:lines="2"  
											:text="item.text" 
											customStyle="width:418rpx;"
						>
						</u--text>
						<view class="d-bc mt-20 calculate">
							<view class="f-regular font_size12 c-regular">该商品已不能购买</view>
								
						</view>
					</view>
				</view>
			</view>
		</block>
	</view>
</template>

<script>
	export default {
		name:"failureGoods",
		props:{
			failureList:Array
		},
		data() {
			return {
				
			}
		},
		methods: {
			
		}
	}
</script>

<style lang="scss" scoped>
	.failure{
		background-color: #fff;
		padding: 30rpx 32rpx 0 32rpx;
		.shopping_box {
			margin-top: 30rpx;
			padding-bottom: 30rpx;
			.shop_box_left {
				margin-right: 20rpx;
				.failure_btn {
					width: 60rpx;
					height: 27rpx;
					margin-right: 12rpx;
					background-color: #f3f1f1;
					border-radius: 14rpx;
				}
				image {
					width: 140rpx;
					height: 140rpx;
					background-color: #666666;
					border-radius: 8rpx;
				}
			}
			.shop_box_right {
				width: 100%;
				.shop_standard {
					width: 317rpx;
					height: 46rpx;
					// padding: 12rpx 18rpx 12rpx 20rpx;
					background-color: #f9f9f9;
					border-radius: 8px;
				}
			}
			.calculate {
				padding: 0 30rpx 0 0;
			}
		}
	}
</style>
